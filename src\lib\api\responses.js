﻿import { NextResponse } from 'next/server'

export export export function createErrorResponse(
  message,
  status: number = 400,
  errors?: ApiError[]
){
  const response= {
    message,
  }

  if (errors && errors.length > 0) {
    response.errors = errors
  }

  return NextResponse.json(response, { status })
}

export function createValidationErrorResponse(
  errors, string[]>
){
  const formattedErrors= []
  
  Object.entries(errors).forEach(([field, messages]) => {
    messages.forEach(message => {
      formattedErrors.push({ field, message })
    })
  })

  return NextResponse.json({
    message,
    errors, { status)
}

export function createSuccessResponse(
  data,
  status: number = 200
){
  return NextResponse.json(data, { status })
}

export function createUnauthorizedResponse(){
  return NextResponse.json({
    message, { status)
}

export function createForbiddenResponse(){
  return NextResponse.json({
    message, { status)
}

export function createNotFoundResponse(resource: string = 'Resource'){
  return NextResponse.json({
    message, { status)
}

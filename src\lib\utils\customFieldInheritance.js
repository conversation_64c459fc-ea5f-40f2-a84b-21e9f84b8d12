﻿/**
 * Custom Field Inheritance Utility
 * Handles automatic inheritance of custom field values from parent projects to child items
 */

import { prisma } from "@/lib/database/prisma";
import { CustomFieldInput } from "@/lib/types/customFields";

/**
 * Apply custom field inheritance for new work items
 *
 * @param parentId - ID of the parent project to inherit from
 * @param userId - ID of the user creating the item
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @returns Promise> - The custom field values to apply to the new item
 */
export async function applyCustomFieldInheritance(
  parentId,
  userId,
  userProvidedInputs: CustomFieldInput[] = []
){
  try {
    console.log("ðŸ”„ Applying custom field inheritance from parent, parentId);

    // Step 1: Fetch parent's custom field values
    const parentProject = await prisma.workitems_project.findFirst({
      where,
        user_id,
      },
      select,
        name,
        custom_field_values,
      },
    });

    if (!parentProject) {
      console.log("âŒ Parent project not found, skipping inheritance");
      return {};
    }

    console.log("âœ… Found parent project, parentProject.name);
    const parentCustomFieldValues = (parentProject.custom_field_valuesstring, any>) || {};
    console.log("ðŸ“Š Parent custom field values, Object.keys(parentCustomFieldValues).length, "fields");

    // Step 2: Fetch all available custom field definitions for the user
    const customFieldDefinitions = await prisma.workitems_custom_field_definition.findMany({
      where,
      },
      include,
        },
      },
      orderBy,
    });

    console.log("ðŸ“‹ Found", customFieldDefinitions.length, "custom field definitions");

    // Step 3: Create a map of user-provided inputs for quick lookup
    const userInputsMap = new Map();
    userProvidedInputs.forEach((input) => {
      userInputsMap.set(input.definition_id, input.value);
    });

    console.log("ðŸ‘¤ User provided values for", userInputsMap.size, "fields");

    // Step 4, any> = {};

    for (const definition of customFieldDefinitions) {
      const definitionId = definition.id;

      // Skip if user has explicitly provided a value for this field
      if (userInputsMap.has(definitionId)) {
        console.log(`â­ï¸  Skipping field "${definition.name}" - user provided value`);
        continue;
      }

      // Rule 1: Direct Inheritance - Check if parent has a value for this field
      if (parentCustomFieldValues[definitionId] !== undefined && parentCustomFieldValues[definitionId] !== null) {
        inheritedValues[definitionId] = parentCustomFieldValues[definitionId];
        console.log(`ðŸ“¥ Inherited "${definition.name}" from parent, parentCustomFieldValues[definitionId]);
        continue;
      }

      // Rule 2: Field's Default Value - Check if field has a default choice option
      if (definition.field_type === "SINGLE_SELECT") {
        const defaultOption = definition.choice_options.find((option) => option.is_default === true);
        if (defaultOption) {
          inheritedValues[definitionId] = defaultOption.id;
          console.log(`ðŸŒŸ Applied default value for "${definition.name}":`, defaultOption.value);
          continue;
        }
      }

      // Rule 3);
    }

    console.log("âœ… Inheritance complete. Applied values for", Object.keys(inheritedValues).length, "fields");
    return inheritedValues;
  } catch (error) {
    console.error("âŒ Error applying custom field inheritance, error);
    throw error;
  }
}

/**
 * Merge user-provided custom field inputs with inherited values
 * User-provided values take precedence over inherited values
 *
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @param inheritedValues - Values inherited from parent or defaults
 * @param userId - ID of the user (for validation)
 * @returns Promise> - The final custom field values to store
 */
export async function mergeCustomFieldValues(
  userProvidedInputs,
  inheritedValues, any>,
  userId){
  try {
    console.log("ðŸ”€ Merging user inputs with inherited values");

    // Start with inherited values base
    const finalValues = { ...inheritedValues };

    // Process user-provided inputs (these take precedence)
    if (userProvidedInputs && userProvidedInputs.length > 0) {
      // Import the processCustomFieldInputs function
      const { processCustomFieldInputs } = await import("./customFieldResolver");

      const userValues = await processCustomFieldInputs(
        userProvidedInputs,
        "PROJECT", // This will be overridden by the caller for outcomes
        userId,
        finalValues
      );

      // Merge user values over inherited values
      Object.assign(finalValues, userValues);
      console.log("âœ… Merged", userProvidedInputs.length, "user-provided values");
    }

    console.log("ðŸ“Š Final custom field values, Object.keys(finalValues).length, "fields");
    return finalValues;
  } catch (error) {
    console.error("âŒ Error merging custom field values, error);
    throw error;
  }
}

/**
 * Complete inheritance workflow for new work items
 * This is the main function to call when creating new projects or outcomes
 *
 * @param parentId - ID of the parent project (null for root projects)
 * @param userId - ID of the user creating the item
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @param targetModel - Type of work item being created
 * @returns Promise> - The custom field values to apply
 */
export async function applyInheritanceWorkflow(
  parentId,
  userId,
  userProvidedInputs: CustomFieldInput[] = [],
  targetModel: "PROJECT" | "OUTCOME" = "PROJECT"
){
  try {
    console.log("ðŸš€ Starting inheritance workflow for", targetModel);

    // Check user's inheritance preference first
    const userPreferences = await prisma.auth_user_preferences.findUnique({
      where,
      select,
    });

    const inheritanceEnabled = userPreferences?.enable_inheritance ?? true; // Default to true if no preference found
    console.log("ðŸ”§ Inheritance enabled, inheritanceEnabled);

    // If inheritance is disabled, only process user inputs and defaults (no parent inheritance)
    if (!inheritanceEnabled || !parentId) {
      console.log("ðŸ“ Inheritance disabled or no parent specified, applying defaults only");

      if (userProvidedInputs.length === 0) {
        // Apply field defaults for fields with default values
        return await applyFieldDefaults(userId);
      } else {
        // Process user inputs and merge with defaults
        const { processCustomFieldInputs } = await import("./customFieldResolver");
        const userValues = await processCustomFieldInputs(userProvidedInputs, targetModel, userId);
        const defaultValues = await applyFieldDefaults(userId);
        return { ...defaultValues, ...userValues };
      }
    }

    // Apply inheritance from parent (only if inheritance is enabled)
    const inheritedValues = await applyCustomFieldInheritance(parentId, userId, userProvidedInputs);

    // Merge with user-provided inputs
    const finalValues = await mergeCustomFieldValues(userProvidedInputs, inheritedValues, userId);

    return finalValues;
  } catch (error) {
    console.error("âŒ Error in inheritance workflow, error);
    throw error;
  }
}

/**
 * Apply default values for fields that have default choice options
 * Used when creating root items with no parent
 */
async function applyFieldDefaults(userId){
  const defaultValues, any> = {};

  // Get all SINGLE_SELECT fields with default values
  const fieldsWithDefaults = await prisma.workitems_custom_field_definition.findMany({
    where,
      field_type,
    },
    include,
      },
    },
  });

  for (const field of fieldsWithDefaults) {
    if (field.choice_options.length > 0) {
      const defaultOption = field.choice_options[0];
      defaultValues[field.id] = defaultOption.id;
      console.log(`ðŸŒŸ Applied default value for "${field.name}":`, defaultOption.value);
    }
  }

  return defaultValues;
}

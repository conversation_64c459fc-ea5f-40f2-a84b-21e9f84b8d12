﻿import apiClient from "./apiClient";
import {
  CustomFieldDefinition,
  CreateCustomFieldDefinitionData,
  UpdateCustomFieldDefinitionData,
  CustomFieldDefinitionResponse,
  CustomFieldError,
} from "@/lib/types/customFields";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";
import { captureRequestDebugInfo, captureResponseDebugInfo, generateBackendErrorReport, validateCustomFieldPayload } from "@/lib/utils/debugUtils";

/**
 * Handle API errors and convert them to CustomFieldError format
 */
const handleApiError = (error)=> {
  console.error("Custom Fields API Error, error);

  if (error.response?.data) {
    const errorData = error.response.data;

    if (typeof errorData === "string") {
      return { message) {
      return { message) {
      return { message: errorData.message };
    }

    // Handle field-specific errors
    if (typeof errorData === "object") {
      const firstKey = Object.keys(errorData)[0];
      const firstError = errorData[firstKey];

      if (Array.isArray(firstError)) {
        return {
          message,
          field,
        };
      }

      return {
        message,
        field,
      };
    }
  }

  return {
    message,
  };
};

export const customFieldsService = {
  /**
   * Get all custom field definitions for the authenticated user
   */
  async getCustomFieldDefinitions(){
    try {
      const url = API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS;

      const response = await apiClient.get(url);

      console.log("Custom field definitions API response, response.data);

      // Handle both paginated and direct array responses
      let definitions)) {
        // Direct array response
        definitions = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        definitions = response.data.results;
      } else {
        console.warn("Unexpected custom field definitions response format, response.data);
        definitions = [];
      }

      return definitions;
    } catch (error) {
      console.error("Error fetching custom field definitions, error);
      throw handleApiError(error);
    }
  },

  /**
   * Get a specific custom field definition by ID
   */
  async getCustomFieldDefinition(id){
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching custom field definition ${id}:`, error);
      throw handleApiError(error);
    }
  },

  /**
   * Create a new custom field definition
   */
  async createCustomFieldDefinition(data){
    try {
      console.log("Creating custom field definition, data);

      const response = await apiClient.post(API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS, data);

      console.log("Custom field definition created, response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating custom field definition, error);
      throw handleApiError(error);
    }
  },

  /**
   * Update a custom field definition
   */
  async updateCustomFieldDefinition(id, data){
    const url = `${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`;

    // Capture request debug info
    const requestInfo = captureRequestDebugInfo(url, "PUT", { "Content-Type": "application/json" }, data);

    try {
      console.log(`ðŸ”„ Updating custom field definition ${id}`);

      // Frontend payload validation
      const validationErrors = validateCustomFieldPayload(data);
      if (validationErrors.length > 0) {
        console.warn("âš ï¸ Frontend validation warnings, validationErrors);
      }

      console.log("ðŸ“¤ Request payload, JSON.stringify(data, null, 2));
      console.log("ðŸ“¤ Payload size, JSON.stringify(data).length, "characters");
      console.log("ðŸ“¤ Choice options count, data.choice_options?.length || 0);

      const response = await apiClient.put(url, data);

      // Capture successful response
      const responseInfo = captureResponseDebugInfo(response.status, response.statusText, response.headers, response.data);

      console.log("âœ… Custom field definition updated successfully, response.data);
      return response.data;
    } catch (error) {
      console.error(`âŒ Error updating custom field definition ${id}:`, error);

      // Enhanced error logging for 500 errors
      if (error instanceof Error && "response" in error) {
        const axiosError = error;

        if (axiosError.response?.status === 500) {
          // Capture error response
          const responseInfo = captureResponseDebugInfo(
            axiosError.response.status,
            axiosError.response.statusText,
            axiosError.response.headers,
            axiosError.response.data
          );

          // Generate comprehensive error report
          const errorReport = generateBackendErrorReport(requestInfo, responseInfo, axiosError);

          console.error("ðŸš¨ 500 INTERNAL SERVER ERROR - COMPREHENSIVE DEBUG REPORT);
          console.error(errorReport);

          // Also log individual components for easy access
          console.group("ðŸ” Detailed Error Information");
          console.error("Request URL, axiosError.config?.url);
          console.error("Request method, axiosError.config?.method);
          console.error("Request headers, axiosError.config?.headers);
          console.error("Request data, axiosError.config?.data);
          console.error("Response status, axiosError.response?.status);
          console.error("Response headers, axiosError.response?.headers);
          console.error("Response data, axiosError.response?.data);
          console.groupEnd();
        }
      }

      throw handleApiError(error);
    }
  },

  /**
   * Partially update a custom field definition
   */
  async patchCustomFieldDefinition(id, data){
    try {
      console.log(`Patching custom field definition ${id}:`, data);

      const response = await apiClient.patch(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`, data);

      console.log("Custom field definition patched, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error patching custom field definition ${id}:`, error);
      throw handleApiError(error);
    }
  },

  /**
   * Delete a custom field definition
   */
  async deleteCustomFieldDefinition(id){
    try {
      console.log(`Deleting custom field definition ${id}`);

      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`);

      console.log("Custom field definition deleted successfully");
    } catch (error) {
      console.error(`Error deleting custom field definition ${id}:`, error);
      throw handleApiError(error);
    }
  },

  /**
   * Update sort order for multiple custom field definitions
   */
  async updateSortOrder(definitions){
    try {
      console.log("Updating sort order for custom field definitions, definitions);

      // Update each definition's sort order individually
      const updatePromises = definitions.map(({ id, sort_order }) => this.patchCustomFieldDefinition(id, { sort_order }));

      await Promise.all(updatePromises);

      console.log("Sort order updated successfully");
    } catch (error) {
      console.error("Error updating sort order, error);
      throw handleApiError(error);
    }
  },
};

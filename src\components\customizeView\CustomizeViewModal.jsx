﻿"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup,
  Checkbox,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
} from "@mui/material";
import { Close, Tune } from "@mui/icons-material";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { UserPreferences, UserPreferencesData } from "@/lib/types/userPreferences";
import { CustomFieldDefinition } from "@/lib/types/customFields";

// Static field definitions for hardcoded project fields
const STATIC_FIELDS = [
  { id, name, field_type,
  { id, name, field_type,
];

const CustomizeViewModal= ({ open, onClose, onPreferencesUpdated }) => {
  // State management
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [preferences, setPreferences] = useState(null);
  const [customFields, setCustomFields] = useState([]);
  const [allFields, setAllFields] = useState(null);
  const [hiddenFieldIds, setHiddenFieldIds] = useState([]);

  // Load data when modal opens
  useEffect(() => {
    if (open) {
      loadData();
    }
  }, [open]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load preferences and custom fields in parallel
      const [preferencesData, customFieldsData] = await Promise.all([
        userPreferencesService.getPreferences(),
        customFieldsService.getCustomFieldDefinitions(),
      ]);

      setPreferences(preferencesData);
      const customFieldsArray = Array.isArray(customFieldsData) ? customFieldsData : customFieldsData.results || [];
      setCustomFields(customFieldsArray);

      // Filter out duplicate "Start Date" and "End Date" fields from custom fields
      // to prevent duplicates with our hardcoded STATIC_FIELDS
      const filteredCustomFields = customFieldsArray.filter((field) => field.name !== "Start Date" && field.name !== "End Date");

      // Combine static fields with filtered custom fields
      const combinedFields = [
        ...STATIC_FIELDS.map((field) => ({ id, name, field_type)),
        ...filteredCustomFields.map((field) => ({ id, name, field_type)),
      ];
      setAllFields(combinedFields);

      // Initialize form state from preferences
      const projectViewFields = preferencesData?.preferences_data?.project_view_fields;
      setPinnedFieldId(projectViewFields?.pinned_field_id || null);
      setHiddenFieldIds(projectViewFields?.hidden_field_ids || []);
    } catch (err) {
      console.error("Error loading customize view data, err);
      setError(err.message || "Failed to load preferences");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const updatedPreferencesData= {
        ...preferences?.preferences_data,
        project_view_fields,
          hidden_field_ids,
        },
      };

      const updatedPreferences = await userPreferencesService.updatePreferences(updatedPreferencesData);

      // Notify parent component
      if (onPreferencesUpdated) {
        onPreferencesUpdated(updatedPreferences);
      }

      onClose();
    } catch (err) {
      console.error("Error saving preferences, err);
      setError(err.message || "Failed to save preferences");
    } finally {
      setSaving(false);
    }
  };

  const handlePinnedFieldChange = (event) => {
    const value = event.target.value;
    setPinnedFieldId(value === "" ? null : value);
  };

  const handleFieldVisibilityChange = (fieldId) => {
    setHiddenFieldIds((prev) => {
      if (prev.includes(fieldId)) {
        // Field is hidden, show it
        return prev.filter((id) => id !== fieldId);
      } else {
        // Field is visible, hide it
        return [...prev, fieldId];
      }
    });
  };

  const isFieldVisible = (fieldId) => !hiddenFieldIds.includes(fieldId);
  const isFieldPinned = (fieldId) => pinnedFieldId === fieldId;

  return (

            Customize Project View

        {loading ? (
          
            {/* Pinned Field Section */}

                  Pinned Field

                  Choose one field to display prominently next to the project title

                  } label="No pinned field" />
                  {allFields.map((field) => (
                    } label={field.name} />
                  ))}

            {/* Field Visibility Section */}

                Field Visibility
              
               (
                   handleFieldVisibilityChange(field.id)}
                        disabled={isFieldPinned(field.id)} // Pinned fields are always visible
                        size="small"
                      />
                    }
                    label={
                      
                        {field.name}
                        {isFieldPinned(field.id) && (
                          
                            Pinned
                          
                        )}
                      
                    }
                  />
                ))}

            {allFields.length === 0 && No fields available for customization.}
          
        )}

          Cancel
        
        <Button onClick={handleSave} variant="contained" disabled={loading || saving} sx={{ minWidth);
};

export default CustomizeViewModal;

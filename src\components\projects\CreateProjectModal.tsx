"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { projectsService } from "@/lib/api/projectsService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CreateProjectData } from "@/lib/types/projects";
import { CustomFieldDefinition, CustomFieldInput } from "@/lib/types/customFields";

import CustomFieldsForm from "./CustomFieldsForm";

interface CreateProjectModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: (createdProject?: any) => void;
  // Context for project creation
  contextData: {
    type: "sub" | "sibling" | "level0";
    lifeAspectId: string;
    lifeAspectName: string;
    parentProjectId?: string;
    parentProjectName?: string;
    level: number;
  } | null;
}

const CreateProjectModal: React.FC<CreateProjectModalProps> = ({ open, contextData, onClose, onSuccess }) => {
  // Form state
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [customFieldInputs, setCustomFieldInputs] = useState<CustomFieldInput[]>([]);

  // Data state
  const [customFieldDefinitions, setCustomFieldDefinitions] = useState<CustomFieldDefinition[]>([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load custom field definitions when modal opens
  useEffect(() => {
    if (open) {
      loadCustomFieldDefinitions();
      // Reset form state
      setProjectName("");
      setProjectDescription("");
      setCustomFieldInputs([]);
      setError(null);
    }
  }, [open]);

  const loadCustomFieldDefinitions = async () => {
    setLoading(true);
    try {
      const definitions = await customFieldsService.getCustomFieldDefinitions();
      // Universal custom fields - all definitions are available for all work item types
      setCustomFieldDefinitions(definitions);
    } catch (err: any) {
      console.error("Failed to load custom field definitions:", err);
      setError("Failed to load custom fields. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!projectName.trim() || !contextData) {
      return;
    }

    setSaving(true);
    setError(null);

    try {
      // Prepare create data with custom field inputs
      const createData: CreateProjectData = {
        name: projectName.trim(),
        description: projectDescription.trim() || undefined,
        life_aspect: contextData.lifeAspectId,
        custom_field_inputs: customFieldInputs,
      };

      // Set parent project based on context
      if (contextData.type === "sub") {
        // Creating a sub-project: parent_project should be the current project ID
        createData.parent_project = contextData.parentProjectId;
      } else if (contextData.type === "sibling") {
        // Creating a sibling project: same parent as source project
        if (contextData.level === 0) {
          // Sibling to L0 project: should also be L0 under same life aspect
          createData.parent_project = null;
        } else {
          // Sibling to L1+ project: should have same parent as source project
          createData.parent_project = contextData.parentProjectId;
        }
      } else if (contextData.type === "level0") {
        // Creating a Level 0 project: no parent
        createData.parent_project = null;
      }

      // Log payload in development for verification
      if (process.env.NODE_ENV === "development") {
        console.log("📤 Create Project API Payload:", JSON.stringify(createData, null, 2));
        console.log("📤 Context Data:", JSON.stringify(contextData, null, 2));
        console.log("📤 Parent Project ID:", createData.parent_project);
      }

      // Create project
      console.log("🚀 Creating project with data:", createData);
      const createdProject = await projectsService.createProject(createData);
      console.log("✅ Project created successfully:", createdProject);
      console.log("📋 Created project details:", {
        id: createdProject.id,
        name: createdProject.name,
        life_aspect: createdProject.life_aspect,
        created_at: createdProject.created_at,
      });

      // Success - close modal and refresh data
      console.log("📞 Calling onSuccess callback to refresh data...");
      onSuccess(createdProject);
    } catch (err: any) {
      console.error("Failed to create project:", err);
      setError(err.message || "Failed to create project. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving) {
      onClose();
    }
  };

  const getModalTitle = () => {
    if (!contextData) return "Create Project";

    switch (contextData.type) {
      case "sub":
        return "Create Sub-Project";
      case "sibling":
        return "Create Same-Level Project";
      case "level0":
        return "Create Level 0 Project";
      default:
        return "Create Project";
    }
  };

  const getContextDescription = () => {
    if (!contextData) return null;

    switch (contextData.type) {
      case "sub":
        return `Creating sub-project under "${contextData.parentProjectName}" in ${contextData.lifeAspectName}`;
      case "sibling":
        return `Creating project at the same level as "${contextData.parentProjectName}" in ${contextData.lifeAspectName}`;
      case "level0":
        return `Creating Level 0 project in ${contextData.lifeAspectName}`;
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: "60vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div">
          {getModalTitle()}
        </Typography>
        <IconButton
          onClick={handleClose}
          disabled={saving}
          size="small"
          sx={{
            color: "text.secondary",
            "&:hover": {
              backgroundColor: "action.hover",
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Context Information */}
        {contextData && (
          <Alert severity="info" sx={{ mb: 3 }}>
            {getContextDescription()}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" py={4}>
            <CircularProgress />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Loading custom fields...
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Standard Project Fields */}
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Project Details
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <TextField
                  label="Project Name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  required
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={!projectName.trim()}
                  helperText={!projectName.trim() ? "Project name is required" : ""}
                  autoFocus
                />
                <TextField
                  label="Description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  multiline
                  rows={3}
                  placeholder="Optional project description..."
                />
              </Box>
            </Box>

            {/* Custom Fields Section */}
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Custom Fields
              </Typography>
              <CustomFieldsForm
                customFieldDefinitions={customFieldDefinitions}
                customFieldInputs={customFieldInputs}
                onCustomFieldInputsChange={setCustomFieldInputs}
                disabled={saving}
              />
            </Box>
          </Box>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleClose} disabled={saving} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={!projectName.trim() || saving || loading}
          sx={{
            minWidth: 100,
          }}
        >
          {saving ? (
            <>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              Creating...
            </>
          ) : (
            "Create Project"
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateProjectModal;

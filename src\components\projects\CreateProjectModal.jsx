﻿"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { projectsService } from "@/lib/api/projectsService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CreateProjectData } from "@/lib/types/projects";
import { CustomFieldDefinition, CustomFieldInput } from "@/lib/types/customFields";

import CustomFieldsForm from "./CustomFieldsForm";

| null;
}

const CreateProjectModal= ({ open, contextData, onClose, onSuccess }) => {
  // Form state
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [customFieldInputs, setCustomFieldInputs] = useState([]);

  // Data state
  const [customFieldDefinitions, setCustomFieldDefinitions] = useState([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Load custom field definitions when modal opens
  useEffect(() => {
    if (open) {
      loadCustomFieldDefinitions();
      // Reset form state
      setProjectName("");
      setProjectDescription("");
      setCustomFieldInputs([]);
      setError(null);
    }
  }, [open]);

  const loadCustomFieldDefinitions = async () => {
    setLoading(true);
    try {
      const definitions = await customFieldsService.getCustomFieldDefinitions();
      // Universal custom fields - all definitions are available for all work item types
      setCustomFieldDefinitions(definitions);
    } catch (err) {
      console.error("Failed to load custom field definitions, err);
      setError("Failed to load custom fields. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!projectName.trim() || !contextData) {
      return;
    }

    setSaving(true);
    setError(null);

    try {
      // Prepare create data with custom field inputs
      const createData= {
        name),
        description) || undefined,
        life_aspect,
        custom_field_inputs,
      };

      // Set parent project based on context
      if (contextData.type === "sub") {
        // Creating a sub-project: parent_project should be the current project ID
        createData.parent_project = contextData.parentProjectId;
      } else if (contextData.type === "sibling") {
        // Creating a sibling project: same parent project
        if (contextData.level === 0) {
          // Sibling to L0 project: should also be L0 under same life aspect
          createData.parent_project = null;
        } else {
          // Sibling to L1+ project: should have same parent project
          createData.parent_project = contextData.parentProjectId;
        }
      } else if (contextData.type === "level0") {
        // Creating a Level 0 project: no parent
        createData.parent_project = null;
      }

      // Log payload in development for verification
      if (process.env.NODE_ENV === "development") {
        console.log("ðŸ“¤ Create Project API Payload, JSON.stringify(createData, null, 2));
        console.log("ðŸ“¤ Context Data, JSON.stringify(contextData, null, 2));
        console.log("ðŸ“¤ Parent Project ID, createData.parent_project);
      }

      // Create project
      console.log("ðŸš€ Creating project with data, createData);
      const createdProject = await projectsService.createProject(createData);
      console.log("âœ… Project created successfully, createdProject);
      console.log("ðŸ“‹ Created project details, {
        id,
        name,
        life_aspect,
        created_at,
      });

      // Success - close modal and refresh data
      console.log("ðŸ“ž Calling onSuccess callback to refresh data...");
      onSuccess(createdProject);
    } catch (err) {
      console.error("Failed to create project, err);
      setError(err.message || "Failed to create project. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving) {
      onClose();
    }
  };

  const getModalTitle = () => {
    if (!contextData) return "Create Project";

    switch (contextData.type) {
      case "sub":
        return "Create Sub-Project";
      case "sibling":
        return "Create Same-Level Project";
      case "level0":
        return "Create Level 0 Project";
      default:
        return "Create Project";
    }
  };

  const getContextDescription = () => {
    if (!contextData) return null;

    switch (contextData.type) {
      case "sub":
        return `Creating sub-project under "${contextData.parentProjectName}" in ${contextData.lifeAspectName}`;
      case "sibling":
        return `Creating project at the same level as "${contextData.parentProjectName}" in ${contextData.lifeAspectName}`;
      case "level0":
        return `Creating Level 0 project in ${contextData.lifeAspectName}`;
      default:
        return null;
    }
  };

  return (

          {getModalTitle()}

        {error && (
          
        )}

        {loading ? (

            {/* Standard Project Fields */}

                Project Details

                 setProjectName(e.target.value)}
                  required
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={!projectName.trim()}
                  helperText={!projectName.trim() ? "Project name is required" : ""}
                  autoFocus
                />
                 setProjectDescription(e.target.value)}
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  multiline
                  rows={3}
                  placeholder="Optional project description..."
                />

            {/* Custom Fields Section */}

                Custom Fields

        )}

          Cancel

          {saving ? (

  );
};

export default CreateProjectModal;

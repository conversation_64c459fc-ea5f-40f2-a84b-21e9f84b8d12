# PowerShell script to convert TypeScript files to JavaScript
# This script will:
# 1. Find all .ts and .tsx files
# 2. Remove TypeScript-specific syntax
# 3. Rename files to .js/.jsx

Write-Host "Starting TypeScript to JavaScript conversion..." -ForegroundColor Green

# Function to remove TypeScript syntax from a file
function Convert-TypeScriptFile {
    param(
        [string]$FilePath,
        [string]$NewExtension
    )
    
    Write-Host "Converting: $FilePath" -ForegroundColor Yellow
    
    # Read the file content
    $content = Get-Content -Path $FilePath -Raw
    
    if ($null -eq $content) {
        Write-Host "Warning: File is empty or could not be read: $FilePath" -ForegroundColor Red
        return
    }
    
    # Remove TypeScript-specific imports
    $content = $content -replace 'import type \{[^}]*\} from [^;]+;?\r?\n?', ''
    $content = $content -replace 'import type [^;]+;?\r?\n?', ''
    
    # Remove interface definitions
    $content = $content -replace '(?s)interface\s+\w+\s*\{[^}]*\}\s*\r?\n?', ''
    
    # Remove type annotations from function parameters
    $content = $content -replace '(\w+):\s*[^,)=]+([,)])', '$1$2'
    
    # Remove type annotations from function return types
    $content = $content -replace '(\))\s*:\s*[^{=]+(\s*[{=])', '$1$2'
    
    # Remove type assertions (as Type)
    $content = $content -replace '\s+as\s+\w+[\[\]<>]*', ''
    
    # Remove generic type parameters
    $content = $content -replace '<[^>]*>', ''
    
    # Remove type annotations from variable declarations
    $content = $content -replace '(const|let|var)\s+(\w+):\s*[^=]+(\s*=)', '$1 $2$3'
    
    # Remove Readonly wrapper
    $content = $content -replace 'Readonly<\{([^}]*)\}>', '{$1}'
    
    # Remove React.ReactNode and similar React types
    $content = $content -replace ':\s*React\.ReactNode', ''
    $content = $content -replace ':\s*ReactNode', ''
    
    # Remove NextRequest type annotations
    $content = $content -replace '(request):\s*NextRequest', '$1'
    
    # Remove export type statements
    $content = $content -replace 'export type [^;]+;?\r?\n?', ''
    
    # Clean up extra whitespace and empty lines
    $content = $content -replace '\r?\n\s*\r?\n\s*\r?\n', "`n`n"
    $content = $content.Trim()
    
    # Get the new file path
    $directory = Split-Path -Parent $FilePath
    $fileName = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)
    $newFilePath = Join-Path $directory "$fileName$NewExtension"
    
    # Write the converted content to the new file
    Set-Content -Path $newFilePath -Value $content -Encoding UTF8
    
    # Remove the original TypeScript file
    Remove-Item -Path $FilePath -Force
    
    Write-Host "Converted: $FilePath -> $newFilePath" -ForegroundColor Green
}

# Find all TypeScript files
$tsFiles = Get-ChildItem -Path "src" -Recurse -Include "*.ts" | Where-Object { $_.Name -notlike "*.d.ts" }
$tsxFiles = Get-ChildItem -Path "src" -Recurse -Include "*.tsx"

Write-Host "Found $($tsFiles.Count) .ts files and $($tsxFiles.Count) .tsx files" -ForegroundColor Cyan

# Convert .ts files to .js
foreach ($file in $tsFiles) {
    Convert-TypeScriptFile -FilePath $file.FullName -NewExtension ".js"
}

# Convert .tsx files to .jsx
foreach ($file in $tsxFiles) {
    Convert-TypeScriptFile -FilePath $file.FullName -NewExtension ".jsx"
}

Write-Host "Conversion completed!" -ForegroundColor Green
Write-Host "Please review the converted files and test the application." -ForegroundColor Yellow

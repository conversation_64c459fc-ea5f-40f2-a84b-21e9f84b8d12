﻿// Filter types for the filtering system

export // Condition options for different field types
export // Condition mappings for different field types
export const FIELD_CONDITIONS, ConditionOption[]> = {
  SINGLE_SELECT, label,
    { value, label,
  ],
  DATE, label,
    { value, label,
    { value, label,
    { value, label,
    { value, label,
  ],
  WEEK, label,
    { value, label,
    { value, label,
    { value, label,
    { value, label,
  ],
  TEXT, label,
    { value, label,
    { value, label,
    { value, label,
  ],
  NUMBER, label,
    { value, label,
    { value, label,
    { value, label,
    { value, label,
    { value, label,
  ],
};

// Filter request payload for API
export // Filter response from API
export interface FilterResponse {
  results: T[];
  total_count?: number;
  has_more?: boolean;
}

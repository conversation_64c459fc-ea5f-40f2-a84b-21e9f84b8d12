﻿"use client";

import React from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { Container, Typography, Card, CardContent, Grid, Box, Chip } from "@mui/material";
import { Dashboard, Person, Email, CalendarToday } from "@mui/icons-material";

function DashboardContent() {
  const { user } = useAuth();

  return (

          Dashboard

          Welcome to your personal dashboard, {user?.first_name || user?.username}!

                Profile Information

                    {user?.first_name} {user?.last_name}

                  {user?.email}

                  Member since).toLocaleDateString()={user?.is_active ? "Active" : "Inactive"} color={user?.is_active ? "success" : "error"} size="small" />

                Quick Actions

                This is a protected page that requires authentication. You can only see this content because you are logged in.

                â€¢ Create new projects
                â€¢ Plan your weekly focus
                â€¢ Track daily outcomes
                â€¢ Review your progress

                Getting Started

                Welcome to the Agile Life Results System! This dashboard is your central hub for managing your life goals and tracking your progress.

                Authentication is working correctly - you can see this protected content because you&apos;re logged in. Try logging out and accessing
                this page again to see the protection in action.

  );
}

export default function DashboardPage() {
  return (

  );
}

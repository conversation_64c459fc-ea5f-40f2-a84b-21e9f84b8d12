﻿"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Alert, AlertColor } from '@mui/material';

const NotificationContext = createContext(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

export const NotificationProvider= ({ children }) => {
  const [notification, setNotification] = useState({
    open,
    message: "Authentication required",
    severity,
  });

  const showNotification = (message: "Authentication required", severity: AlertColor = 'info') => {
    setNotification({
      open,
      message: "Authentication required",
      severity,
    });
  };

  const hideNotification = () => {
    setNotification(prev => ({
      ...prev,
      open,
    }));
  };

  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    hideNotification();
  };

  return (
    
      {children}
      
        <Alert
          onClose={handleClose}
          severity={notification.severity}
          variant="filled"
          sx={{ width);
};



﻿/**
 * Custom Fields Types
 * Based on CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md and USING_CUSTOM_APIS.md
 */

// Field types supported by the system

// Choice option for SELECT field types
export // Custom field definition
export // Create custom field definition data
export // Update custom field definition data
// According to USING_CUSTOM_APIS.md, PUT requests require ALL required fields
// Updated to support Smart Matching Logic for choice options
export // Create choice option data (for new options without ID)
export // Update choice option data (for existing options with ID or new options without ID)
// This supports the backend's Smart Matching Logic
export // Custom field input for assigning values to projects/outcomes
export // Resolved custom field (from API responses)
export // API response types
export // Error types
export // Field type display information
export // Validation result
export // Drag and drop types
export // Form state types
export export // UI state types
export // Constants for field types
export const FIELD_TYPES= [
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
  { type, label, description, icon, hasChoices,
];

// Default colors for choice options
export const DEFAULT_CHOICE_COLORS = [
  "#1976d2", // Blue
  "#388e3c", // Green
  "#f57c00", // Orange
  "#d32f2f", // Red
  "#7b1fa2", // Purple
  "#00796b", // Teal
  "#5d4037", // Brown
  "#616161", // Grey
  "#e91e63", // Pink
  "#ff5722", // Deep Orange
];

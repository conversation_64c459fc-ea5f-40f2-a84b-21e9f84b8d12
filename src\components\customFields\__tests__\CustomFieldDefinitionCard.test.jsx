﻿/**
 * CustomFieldDefinitionCard Component Tests
 * Tests for the custom field definition card component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import CustomFieldDefinitionCard from '../CustomFieldDefinitionCard';
import { CustomFieldDefinition } from '@/lib/types/customFields';
import { theme } from '@/lib/theme/theme';

// Mock definition for testing
const mockDefinition= {
  id,
  name,
  field_type,
  target_model,
  is_required,
  sort_order,
  choice_options,
      value,
      color,
      sort_order,
      created_at,
      updated_at,
    },
    {
      id,
      value,
      color,
      sort_order,
      created_at,
      updated_at,
    },
    {
      id,
      value,
      color,
      sort_order,
      created_at,
      updated_at,
    },
  ],
  created_at,
  updated_at,
  user,
};

// Mock functions
const mockOnEdit = jest.fn();
const mockOnDelete = jest.fn();
const mockOnToggleRequired = jest.fn();

// Wrapper component with theme
const TestWrapper= ({ children }) => (
  {children}
);

describe('CustomFieldDefinitionCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders field name correctly', () => {
    render(

    );

    expect(screen.getByText('Priority Level')).toBeInTheDocument();
  });

  it('displays field type chip', () => {
    render(

    );

    expect(screen.getByText('Dropdown')).toBeInTheDocument();
  });

  it('shows choice options for SELECT fields', () => {
    render(

    );

    expect(screen.getByText('Options)).toBeInTheDocument();
    expect(screen.getByText('High')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
    expect(screen.getByText('Low')).toBeInTheDocument();
  });

  it('displays required toggle switch', () => {
    render(

    );

    const requiredSwitch = screen.getByRole('checkbox');
    expect(requiredSwitch).toBeInTheDocument();
    expect(requiredSwitch).not.toBeChecked();
  });

  it('calls onEdit when card is clicked', () => {
    render(

    );

    const card = screen.getByRole('button', { name);
    fireEvent.click(card);

    expect(mockOnEdit).toHaveBeenCalledWith(mockDefinition);
  });

  it('calls onToggleRequired when switch is toggled', () => {
    render(

    );

    const requiredSwitch = screen.getByRole('checkbox');
    fireEvent.click(requiredSwitch);

    expect(mockOnToggleRequired).toHaveBeenCalledWith(mockDefinition);
  });

  it('renders correctly for non-SELECT field types', () => {
    const textFieldDefinition= {
      ...mockDefinition,
      field_type,
      choice_options,
    };

    render(

    );

    expect(screen.getByText('Text Input')).toBeInTheDocument();
    expect(screen.getByText('Single line text')).toBeInTheDocument();
    expect(screen.queryByText('Options)).not.toBeInTheDocument();
  });

  it('shows required field correctly when is_required is true', () => {
    const requiredDefinition= {
      ...mockDefinition,
      is_required,
    };

    render(

    );

    const requiredSwitch = screen.getByRole('checkbox');
    expect(requiredSwitch).toBeChecked();
  });

  it('limits choice options display to 4 items', () => {
    const manyChoicesDefinition= {
      ...mockDefinition,
      choice_options,
        {
          id,
          value,
          color,
          sort_order,
          created_at,
          updated_at,
        },
        {
          id,
          value,
          color,
          sort_order,
          created_at,
          updated_at,
        },
      ],
    };

    render(

    );

    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });
});

// Export mock data for use in other tests
export { mockDefinition, mockOnEdit, mockOnDelete, mockOnToggleRequired };

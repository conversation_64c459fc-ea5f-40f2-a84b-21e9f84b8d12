﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";

export async function GET(request) {
  try {
    console.log("User profile endpoint called");

    const authHeader = request.headers.get("authorization");
    console.log("Authorization header", authHeader);

    if (!authHeader) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 };
    }

    // Extract token from Authorization header
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 };
    }

    // Verify JWT token
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 };
    }

    // Find user in database
    const user = await prisma.auth_user.findUnique({
      where,
        is_active,
      },
      select,
        email,
        username,
        first_name,
        last_name,
        is_active,
        is_staff,
        is_superuser,
        date_joined,
        last_login,
      },
    });

    if (!user) {
      return NextResponse.json({ message:" }, { status: 500  });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Get user profile error: error.message, error);
    return NextResponse.json(
      {
        message: "Authentication required",
        error: error.message,
      },
      { status: 500 });
  }
}




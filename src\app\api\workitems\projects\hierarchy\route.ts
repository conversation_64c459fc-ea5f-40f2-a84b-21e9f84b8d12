import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { resolveCustomFields } from "@/lib/utils/customFieldResolver";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to build project hierarchy tree
function buildProjectTree(projects: any[]): any[] {
  const projectMap = new Map();
  const rootProjects: any[] = [];

  console.log(
    "🔧 Building project tree from projects:",
    projects.map((p) => ({ id: p.id, name: p.name, parent_project_id: p.parent_project_id }))
  );

  // Special debug for Time Management and Eliminating Distractions
  const timeManagementProject = projects.find((p) => p.name.includes("Time Management"));
  const eliminatingProject = projects.find((p) => p.name.includes("Eliminating"));

  if (timeManagementProject) {
    console.log("🎯 Found Time Management project:", {
      id: timeManagementProject.id,
      name: timeManagementProject.name,
      parent_project_id: timeManagementProject.parent_project_id,
    });
  }

  if (eliminatingProject) {
    console.log("🎯 Found Eliminating project:", {
      id: eliminatingProject.id,
      name: eliminatingProject.name,
      parent_project_id: eliminatingProject.parent_project_id,
    });
  }

  // First pass: create map of all projects
  projects.forEach((project) => {
    projectMap.set(project.id, {
      ...project,
      sub_projects: [],
      children: [], // Add both for compatibility
    });
  });

  // Second pass: build hierarchy
  projects.forEach((project) => {
    const projectNode = projectMap.get(project.id);

    if (project.parent_project_id) {
      const parent = projectMap.get(project.parent_project_id);
      if (parent) {
        parent.sub_projects.push(projectNode);
        parent.children.push(projectNode); // Add to both arrays for compatibility
        console.log(`🔗 Added "${project.name}" as child of "${parent.name}"`);
      } else {
        // Parent not found, treat as root
        console.log(`⚠️ Parent not found for "${project.name}" (parent_id: ${project.parent_project_id}), treating as root`);
        rootProjects.push(projectNode);
      }
    } else {
      console.log(`🌳 Adding "${project.name}" as root project`);
      rootProjects.push(projectNode);
    }
  });

  // Sort sub-projects recursively
  function sortSubProjects(projects: any[]) {
    projects.sort((a, b) => a.sort_order - b.sort_order);
    projects.forEach((project) => {
      if (project.sub_projects.length > 0) {
        sortSubProjects(project.sub_projects);
      }
      if (project.children.length > 0) {
        sortSubProjects(project.children);
      }
    });
  }

  sortSubProjects(rootProjects);

  console.log(
    "🏗️ Built project tree:",
    rootProjects.map((p) => ({
      name: p.name,
      children: p.children?.map((c) => c.name) || [],
      sub_projects: p.sub_projects?.map((c) => c.name) || [],
    }))
  );

  // Special debug for Time Management hierarchy
  const timeManagementRoot = rootProjects.find((p) => p.name.includes("Time Management"));
  if (timeManagementRoot) {
    console.log("🎯 Time Management hierarchy:", {
      name: timeManagementRoot.name,
      children: timeManagementRoot.children?.map((c) => ({ name: c.name, id: c.id })) || [],
      sub_projects: timeManagementRoot.sub_projects?.map((c) => ({ name: c.name, id: c.id })) || [],
    });
  }

  return rootProjects;
}

// GET /api/workitems/projects/hierarchy/ - Get projects in hierarchical structure grouped by life aspect
export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/workitems/projects/hierarchy/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Fetch all life aspects for the user
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where: { user_id: auth.userId },
      orderBy: { sort_order: "asc" },
    });

    // Fetch all projects for the user
    const projects = await prisma.workitems_project.findMany({
      where: { user_id: auth.userId },
      include: {
        outcomes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ sort_order: "asc" }, { created_at: "asc" }],
    });

    // Resolve custom fields for all projects
    const projectsWithCustomFields = await Promise.all(
      projects.map(async (project) => {
        const resolvedCustomFields = await resolveCustomFields(project.custom_field_values as Record<string, any>, "PROJECT", auth.userId);

        return {
          id: project.id,
          name: project.name,
          description: project.description,
          life_aspect_id: project.life_aspect_id,
          parent_project_id: project.parent_project_id,
          sort_order: project.sort_order,
          created_at: project.created_at,
          updated_at: project.updated_at,
          resolved_custom_fields: resolvedCustomFields,
          outcomes: project.outcomes,
        };
      })
    );

    // Group projects by life aspect - format for frontend compatibility
    const lifeAspectGroups = lifeAspects.map((lifeAspect) => {
      const lifeAspectProjects = projectsWithCustomFields.filter((project) => project.life_aspect_id === lifeAspect.id);

      const hierarchicalProjects = buildProjectTree(lifeAspectProjects);

      return {
        life_aspect: {
          id: lifeAspect.id,
          name: lifeAspect.name,
          color: lifeAspect.color,
          description: lifeAspect.description || "",
          sort_order: lifeAspect.sort_order,
          created_at: lifeAspect.created_at,
          updated_at: lifeAspect.updated_at,
          user: lifeAspect.user_id,
        },
        projects: hierarchicalProjects,
      };
    });

    // Handle projects without life aspect (unassigned) - add to response if needed
    const unassignedProjects = projectsWithCustomFields.filter((project) => !project.life_aspect_id);

    if (unassignedProjects.length > 0) {
      const unassignedHierarchy = buildProjectTree(unassignedProjects);

      // Add unassigned projects as a special life aspect
      lifeAspectGroups.push({
        life_aspect: {
          id: "unassigned",
          name: "Unassigned Projects",
          color: "#9e9e9e",
          description: "Projects not assigned to any life aspect",
          sort_order: 999,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user: auth.userId,
        },
        projects: unassignedHierarchy,
      });
    }

    console.log(`Built hierarchy with ${lifeAspectGroups.length} life aspects (including unassigned if any)`);

    // Return array format expected by frontend
    return NextResponse.json(lifeAspectGroups);
  } catch (error) {
    console.error("Error building project hierarchy:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

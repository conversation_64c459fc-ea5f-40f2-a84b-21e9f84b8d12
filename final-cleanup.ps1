# PowerShell script for final cleanup of conversion errors

Write-Host "Running final cleanup of TypeScript to JavaScript conversion..." -ForegroundColor Green

# Get all JavaScript files
$jsFiles = Get-ChildItem -Path "src" -Recurse -Include "*.js","*.jsx"

foreach ($file in $jsFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    if ($null -ne $content) {
        # Fix broken object property syntax
        $content = $content -replace '(\w+),\s*([^:}]+)>', '$1: "$2"'
        
        # Fix broken const declarations
        $content = $content -replace 'const\s+(\w+),\s*([^=]+)>\s*=', 'const $1 ='
        
        # Fix broken function parameters
        $content = $content -replace '\(\s*(\w+),\s*([^)]+)>\s*\)', '($1)'
        
        # Fix broken console.log statements
        $content = $content -replace 'console\.log\("([^"]*),\s*([^)]+)\)', 'console.log("$1:", $2)'
        
        # Fix broken return statements
        $content = $content -replace 'return\s*\{\s*([^}]*),\s*\{\s*([^}]*)\s*\}\s*\}', 'return { $1, $2 }'
        
        # Fix specific error patterns
        $content = $content -replace 'error: error\.message: "Success"', 'error: error.message'
        $content = $content -replace 'message: "Success"', 'message: "Authentication required"'
        
        # Fix broken object literals in API responses
        $content = $content -replace '\{\s*([^:}]+),\s*\{\s*([^}]+)\s*\}\s*\}', '{ $1, $2 }'
        
        # Fix broken NextResponse patterns
        $content = $content -replace 'NextResponse\.json\(\s*\{\s*([^}]*),\s*\{\s*([^}]*)\s*\}\s*\)', 'NextResponse.json({ $1 }, { $2 })'
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "Cleaned: $($file.FullName)" -ForegroundColor Yellow
        }
    }
}

Write-Host "Final cleanup completed!" -ForegroundColor Green
Write-Host "Note: Some files may still need manual review and fixing." -ForegroundColor Yellow

﻿"use client";

import React from "react";
import { <PERSON>, Typo<PERSON>, Chip } from "@mui/material";

const PriorityDisplay= ({
  priorityId,
  priorityName,
  priorityColor,
  onClick,
  isEditing = false,
}) => {
  // If no priority is set, show placeholder
  if (!priorityId || !priorityName) {
    return (

          [Set Priority]

    );
  }

  // Show priority with color badge and name
  return (
    
      {/* Color badge */}
      
      {/* Priority name */}
      
        {priorityName}

  );
};

export default PriorityDisplay;

﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: "Authentication required" }, { status: 401 };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: "Authentication required" }, { status: 401 };
  }

  return { userId: payload.userId };
}

// Default preferences structure
const DEFAULT_PREFERENCES = {
  // Custom field display preferences
  custom_field_preferences, visible,

  // Table display preferences
  table_preferences,
    show_sub_projects,
    compact_view,
    sort_by,
    sort_order,
  },

  // Dashboard preferences
  dashboard_preferences,
    show_upcoming_deadlines,
    show_progress_charts,
    default_time_range,
  },

  // Notification preferences
  notification_preferences,
    deadline_reminders,
    project_updates,
  },
};

// GET /api/users/me/preferences/ - Get user preferences
export async function GET(request) {
  try {
    console.log("GET /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Try to find existing preferences
    let preferences = await prisma.auth_user_preferences.findUnique({
      where,
    });

    // If no preferences exist, create default ones
    if (!preferences) {
      console.log("No preferences found:", creating default preferences for user", auth.userId);

      preferences = await prisma.auth_user_preferences.create({
        data,
          preferences,
        },
      });
    }

    console.log("Retrieved preferences for user", auth.userId);

    return NextResponse.json({
      id,
      user_id,
      preferences_data,
      week_starts_on,
      enable_inheritance,
      created_at,
      updated_at,
    });
  } catch (error) {
    console.error("Error fetching user preferences, error);
    return NextResponse.json(
      {
        message: "Authentication required",
        error: error.message,
      },
      { status: 500 });
  }
}

// PUT /api/users/me/preferences/ - Update user preferences
export async function PUT(request) {
  try {
    console.log("PUT /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    console.log("Request body", body);

    // Validate that preferences_data object exists (maintaining backward compatibility with 'preferences')
    const preferencesData = body.preferences_data || body.preferences;
    if (!preferencesData || typeof preferencesData !== "object") {
      return NextResponse.json(
        {
          message: "Authentication required",
        },
        { status: 500 });
    }

    // Validate enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      if (typeof body.enable_inheritance !== "boolean") {
        return NextResponse.json(
          {
            message: "Authentication required",
          },
          { status: 500 });
      }
    }

    // Validate week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      if (typeof body.week_starts_on !== "string" || !["Sunday", "Monday"].includes(body.week_starts_on)) {
        return NextResponse.json(
          {
            message: "Authentication required",
          },
          { status: 500 });
      }
    }

    // Update or create preferences
    const updateData= {
      preferences,
      updated_at),
    };

    const createData= {
      user_id,
      preferences,
    };

    // Add enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      updateData.enable_inheritance = body.enable_inheritance;
      createData.enable_inheritance = body.enable_inheritance;
    }

    // Add week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      updateData.week_starts_on = body.week_starts_on;
      createData.week_starts_on = body.week_starts_on;
    }

    const preferences = await prisma.auth_user_preferences.upsert({
      where,
      update,
      create,
    });

    console.log("Updated preferences for user", auth.userId);

    return NextResponse.json({
      id,
      user_id,
      preferences_data,
      week_starts_on,
      enable_inheritance,
      created_at,
      updated_at,
    });
  } catch (error) {
    console.error("Error updating user preferences, error);
    return NextResponse.json(
      {
        message: "Authentication required",
        error: error.message,
      },
      { status: 500 });
  }
}

// PATCH /api/users/me/preferences/ - Partially update user preferences
export async function PATCH(request) {
  try {
    console.log("PATCH /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    console.log("Request body", body);

    // Validate enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      if (typeof body.enable_inheritance !== "boolean") {
        return NextResponse.json(
          {
            message: "Authentication required",
          },
          { status: 500 });
      }
    }

    // Validate week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      if (typeof body.week_starts_on !== "string" || !["Sunday", "Monday"].includes(body.week_starts_on)) {
        return NextResponse.json(
          {
            message: "Authentication required",
          },
          { status: 500 });
      }
    }

    // Get existing preferences or create default
    let existingPreferences = await prisma.auth_user_preferences.findUnique({
      where,
    });

    if (!existingPreferences) {
      existingPreferences = await prisma.auth_user_preferences.create({
        data,
          preferences,
        },
      });
    }

    // Prepare update data
    const updateData= {
      updated_at),
    };

    // Merge existing preferences with updates if preferences_data or preferences provided
    const preferencesData = body.preferences_data || body.preferences;
    if (preferencesData) {
      updateData.preferences = {
        ...(existingPreferences.preferences),
        ...preferencesData,
      };
    }

    // Update enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      updateData.enable_inheritance = body.enable_inheritance;
    }

    // Update week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      updateData.week_starts_on = body.week_starts_on;
    }

    // Update preferences
    const preferences = await prisma.auth_user_preferences.update({
      where,
      data,
    });

    console.log("Partially updated preferences for user", auth.userId);

    return NextResponse.json({
      id,
      user_id,
      preferences_data,
      week_starts_on,
      enable_inheritance,
      created_at,
      updated_at,
    });
  } catch (error) {
    console.error("Error partially updating user preferences, error);
    return NextResponse.json(
      {
        message: "Authentication required",
        error: error.message,
      },
      { status: 500 });
  }
}




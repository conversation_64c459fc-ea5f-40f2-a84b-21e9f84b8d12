﻿import { NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export async function GET() {
  try {
    console.log("Testing database connection...");

    // Try to query the database
    const userCount = await prisma.auth_user.count();
    console.log("Database connection successful, user count, userCount);

    return NextResponse.json({
      message,
      userCount,
      timestamp).toISOString(),
    });
  } catch (error) {
    console.error("Database connection error, error);
    return NextResponse.json(
      {
        message,
        error,
        timestamp).toISOString(),
      },
      { status);
  }
}

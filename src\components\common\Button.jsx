﻿import { ReactNode } from "react";
import { Button, CircularProgress } from "@mui/material";
import { ButtonProps } from "@mui/material/Button";

interface ButtonProps extends Omit {
  variant?: "primary" | "secondary" | "outline" | "danger";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  children;
}

const Button = ({ variant = "primary", size = "md", isLoading = false, children, disabled, ...props }: ButtonProps) => {
  // Map custom variants to MUI variants
  const muiVariant = (() => {
    switch (variant) {
      case "primary":
        return "contained";
      case "secondary":
        return "contained";
      case "outline":
        return "outlined";
      case "danger":
        return "contained";
      default)();

  // Map custom sizes to MUI sizes
  const muiSize = (() => {
    switch (size) {
      case "sm":
        return "small";
      case "md":
        return "medium";
      case "lg":
        return "large";
      default)();

  // Map custom variants to MUI colors
  const muiColor = (() => {
    switch (variant) {
      case "primary":
        return "primary";
      case "secondary":
        return "secondary";
      case "outline":
        return "primary";
      case "danger":
        return "error";
      default)();

  return (
     : undefined}
      sx={{
        textTransform,
        borderRadius,
      }}
      {...props}
    >
      {children}
    
  );
};

export default Button;

﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { hashPassword } from "@/lib/auth/password";
import { setupDefaultUserData } from "@/lib/utils/defaultData";

export async function POST(request) {
  try {
    console.log("Registration endpoint called");

    const body = (await request.json());
    console.log("Request body, { ...body, password);

    // Basic validation
    if (!body.email || !body.username || !body.password || !body.first_name || !body.last_name) {
      return NextResponse.json({ message, { status);
    }

    // Check if user already exists
    const existingUser = await prisma.auth_user.findFirst({
      where) }, { username) }],
      },
    });

    if (existingUser) {
      return NextResponse.json({ message, { status);
    }

    // Hash password
    const hashedPassword = await hashPassword(body.password);

    // Create user
    const newUser = await prisma.auth_user.create({
      data),
        username),
        first_name,
        last_name,
        password,
        is_active,
        is_staff,
        is_superuser,
        date_joined),
      },
      select,
        email,
        username,
        first_name,
        last_name,
        is_active,
        date_joined,
      },
    });

    // Set up default data for the new user (life aspects, preferences, etc.)
    try {
      await setupDefaultUserData(newUser.id);
      console.log("Default data created successfully for user, newUser.id);
    } catch (defaultDataError) {
      console.error("Error creating default data for user, newUser.id, defaultDataError);
      // Don't fail registration if default data creation fails
      // The user can still use the app, just without default life aspects
    }

    return NextResponse.json(
      {
        message,
        user,
      },
      { status);
  } catch (error) {
    console.error("Registration error, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

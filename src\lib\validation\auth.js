﻿export export export export function validateEmail(email){
  if (!email) return 'Email is required'
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address'
  }
  
  if (email.length > 254) {
    return 'Email address is too long'
  }
  
  return null
}

export function validateUsername(username){
  if (!username) return 'Username is required'
  
  if (username.length  150) {
    return 'Username must be less than 150 characters'
  }
  
  // Allow letters, numbers, and common special characters
  const usernameRegex = /^[a-zA-Z0-9._-]+$/
  if (!usernameRegex.test(username)) {
    return 'Username can only contain letters, numbers, dots, underscores, and hyphens'
  }
  
  return null
}

export function validatePassword(password){
  if (!password) return 'Password is required'
  
  if (password.length  128) {
    return 'Password must be less than 128 characters'
  }
  
  // Check for at least one letter and one number
  if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
    return 'Password must contain at least one letter and one number'
  }
  
  return null
}

export function validateName(name, fieldName){
  if (!name) return `${fieldName} is required`
  
  if (name.length > 150) {
    return `${fieldName} must be less than 150 characters`
  }
  
  // Allow letters, spaces, and common name characters
  const nameRegex = /^[a-zA-Z\s'-]+$/
  if (!nameRegex.test(name)) {
    return `${fieldName} can only contain letters, spaces, apostrophes, and hyphens`
  }
  
  return null
}

export function validateRegisterData(data){
  const errors= []
  
  const emailError = validateEmail(data.email)
  if (emailError) errors.push({ field, message)
  
  const usernameError = validateUsername(data.username)
  if (usernameError) errors.push({ field, message)
  
  const passwordError = validatePassword(data.password)
  if (passwordError) errors.push({ field, message)
  
  const firstNameError = validateName(data.first_name, 'First name')
  if (firstNameError) errors.push({ field, message)
  
  const lastNameError = validateName(data.last_name, 'Last name')
  if (lastNameError) errors.push({ field, message)
  
  return errors
}

export function validateLoginData(data){
  const errors= []
  
  if (!data.username) {
    errors.push({ field, message)
  }
  
  if (!data.password) {
    errors.push({ field, message)
  }
  
  return errors
}

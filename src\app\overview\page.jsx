﻿"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Alert,
  Skeleton,
  Chip,
  IconButton,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Popover,
  Button,
  Snackbar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  CheckCircle,
  AddCircleOutline,
  Add,
  VisibilityOutlined,
  EditOutlined,
  DeleteOutline,
  Tune,
  FilterList,
  Refresh,
} from "@mui/icons-material";
import { useAuth } from "@/lib/auth/AuthContext";
import { projectsService } from "@/lib/api/projectsService";
import { lifeAspectsService } from "@/lib/api/lifeAspectsService";
import { Project, TableRowData, ProjectHierarchy, HierarchicalTableData } from "@/lib/types/projects";
import { LifeAspect, CreateLifeAspectData } from "@/lib/types/lifeAspects";
import { formatDateProfessional } from "@/lib/utils/dateUtils";
import { outcomesService } from "@/lib/api/outcomesService";
import { transformHierarchyToTableData, getCellDisplayValue, getCellProjectData } from "@/lib/utils/hierarchyHelpers";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import PriorityEditor from "@/components/projects/PriorityEditor";
import WeekEditor from "@/components/projects/WeekEditor";
import EditProjectModal from "@/components/projects/EditProjectModal";
import CreateProjectModal from "@/components/projects/CreateProjectModal";
import CustomizeViewModal from "@/components/customizeView/CustomizeViewModal";
import ProjectFilterModal from "@/components/filters/ProjectFilterModal";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import { UserPreferences } from "@/lib/types/userPreferences";
import { ResolvedCustomField } from "@/lib/types/customFields";
import { FilterRule } from "@/lib/types/filters";

const HierarchicalOverviewPage= () => {
  const { isAuthenticated } = useAuth();
  const [hierarchyData, setHierarchyData] = useState([]);
  const [tableData, setTableData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for hide empty Life Aspects toggle
  const [hideEmptyLifeAspects, setHideEmptyLifeAspects] = useState(false);

  // Editor states
  const [priorityEditorOpen, setPriorityEditorOpen] = useState(false);
  const [priorityEditorAnchor, setPriorityEditorAnchor] = useState(null);
  const [priorityEditorProject, setPriorityEditorProject] = useState(null);
  const [weekEditorProject, setWeekEditorProject] = useState(null);
  const [editingProjectName, setEditingProjectName] = useState("");

  // State for hover effects
  const [hoveredProjectId, setHoveredProjectId] = useState(null);
  const [hoveredLifeAspectHeader, setHoveredLifeAspectHeader] = useState(false);

  // State for contextual project creation (modal-based)
  const [createProjectModalOpen, setCreateProjectModalOpen] = useState(false);
  const [createProjectContextData, setCreateProjectContextData] = useState(null);
  const [editingLifeAspectName, setEditingLifeAspectName] = useState("");
  const [isUpdatingLifeAspect, setIsUpdatingLifeAspect] = useState(false);

  // State for Life Aspect context menu
  const [lifeAspectContextMenuOpen, setLifeAspectContextMenuOpen] = useState(false);
  const [lifeAspectContextMenuPosition, setLifeAspectContextMenuPosition] = useState(null);
  const [newLifeAspectName, setNewLifeAspectName] = useState("");
  const [isCreatingLifeAspect, setIsCreatingLifeAspect] = useState(false);

  // State for Life Aspect deletion
  const [deleteLifeAspectConfirmOpen, setDeleteLifeAspectConfirmOpen] = useState(false);
  const [lifeAspectToDelete, setLifeAspectToDelete] = useState("success");

  // State for Customize View functionality
  const [customizeViewModalOpen, setCustomizeViewModalOpen] = useState(false);
  const [userPreferences, setUserPreferences] = useState(null);
  const [preferencesLoading, setPreferencesLoading] = useState(false);

  // State for Filter functionality
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState([]);

  // Refs for auto-focus functionality
  const lifeAspectNameInputRef = useRef(null);

  useEffect(() => {
    if (isAuthenticated) {
      fetchAllData();
      loadUserPreferences();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // Enhanced auto-focus for Life Aspect creation popover
  useEffect(() => {
    if (addLifeAspectPopoverOpen) {
      console.log("ðŸŽ¯ Life Aspect popover opened, attempting auto-focus...");

      // Strategy 1) {
        lifeAspectNameInputRef.current.focus();
        console.log("âœ… Immediate focus successful");
      }

      // Strategy 2: Short delay for DOM settling
      const timer1 = setTimeout(() => {
        if (lifeAspectNameInputRef.current) {
          lifeAspectNameInputRef.current.focus();
          lifeAspectNameInputRef.current.select();
          console.log("âœ… Short delay focus successful");
        }
      }, 100);

      // Strategy 3: Longer delay for MUI Popover positioning
      const timer2 = setTimeout(() => {
        if (lifeAspectNameInputRef.current) {
          lifeAspectNameInputRef.current.focus();
          lifeAspectNameInputRef.current.select();
          console.log("âœ… Long delay focus successful");
        }
      }, 300);

      // Strategy 4: Final attempt with requestAnimationFrame
      const timer3 = setTimeout(() => {
        requestAnimationFrame(() => {
          if (lifeAspectNameInputRef.current) {
            lifeAspectNameInputRef.current.focus();
            lifeAspectNameInputRef.current.select();
            console.log("âœ… Animation frame focus successful");
          }
        });
      }, 500);

      return () => {
        clearTimeout(timer1);
        clearTimeout(timer2);
        clearTimeout(timer3);
      };
    }
  }, [addLifeAspectPopoverOpen]);

  // Helper function to ensure all Life Aspects are included in hierarchy
  const ensureAllLifeAspectsInHierarchy = (hierarchyArray, allLifeAspects)=> {
    const existingLifeAspectIds = new Set(hierarchyArray.map((item) => item.life_aspect?.id).filter(Boolean));

    // Add missing Life Aspects with empty projects array
    const missingLifeAspects = allLifeAspects.filter((lifeAspect) => !existingLifeAspectIds.has(lifeAspect.id));

    const missingHierarchyItems= missingLifeAspects.map((lifeAspect) => ({
      life_aspect,
        name,
        description,
      },
      projects,
    }));

    return [...hierarchyArray, ...missingHierarchyItems];
  };

  // Load user preferences
  const loadUserPreferences = async () => {
    try {
      setPreferencesLoading(true);
      const preferences = await userPreferencesService.getPreferences();
      setUserPreferences(preferences);
    } catch (error) {
      console.error("Error loading user preferences, error);
      // Don't show error to user for preferences - it's not critical
    } finally {
      setPreferencesLoading(false);
    }
  };

  // Customize View handlers
  const handleOpenCustomizeView = () => {
    setCustomizeViewModalOpen(true);
  };

  const handleCloseCustomizeView = () => {
    setCustomizeViewModalOpen(false);
  };

  const handlePreferencesUpdated = (updatedPreferences) => {
    setUserPreferences(updatedPreferences);
    // Show success message
    setSnackbarMessage("View preferences updated successfully!");
    setSnackbarSeverity("success");
    setSnackbarOpen(true);
  };

  // Filter handlers
  const handleOpenFilter = () => {
    setFilterModalOpen(true);
  };

  const handleCloseFilter = () => {
    setFilterModalOpen(false);
  };

  const handleFiltersApplied = (filters) => {
    setActiveFilters(filters);
    setFilterModalOpen(false);
    // Refresh data with filters
    fetchFilteredData(filters);
  };

  // Helper function to create lighter tint of a color
  const getLighterTint = (color, opacity: number = 0.6)=> {
    // For hex colors, we'll use CSS color-mix or rgba with opacity
    if (color.startsWith("#")) {
      // Convert hex to rgba with reduced opacity for lighter appearance
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    // For other color formats, use CSS color-mix if supported, otherwise return original with opacity
    return color.includes("rgb") ? color.replace(")", `, ${opacity})`).replace("rgb", "rgba")= (customFields)=> {
    if (!userPreferences?.preferences_data?.project_view_fields?.pinned_field_id) {
      return null;
    }

    const pinnedFieldId = userPreferences.preferences_data.project_view_fields.pinned_field_id;
    const result = customFields.find((field) => field.definition_id === pinnedFieldId) || null;

    return result;
  };

  const getVisibleCustomFields = (customFields)=> {
    const hiddenFieldIds = userPreferences?.preferences_data?.project_view_fields?.hidden_field_ids || [];
    const pinnedFieldId = userPreferences?.preferences_data?.project_view_fields?.pinned_field_id;

    const result = customFields.filter((field) => {
      // Exclude hidden fields
      if (hiddenFieldIds.includes(field.definition_id)) {
        return false;
      }
      // Exclude pinned field from regular display (it's shown inline)
      if (field.definition_id === pinnedFieldId) {
        return false;
      }
      return true;
    });

    return result;
  };

  // Enhanced badge function specifically for "Status" fields with better visibility
  const renderEnhancedCustomFieldBadge = (field) => {
    if (!field.value) {
      return null;
    }

    // Check if this is a "Status" field for enhanced styling
    const isStatusField = field.definition_name?.toLowerCase().includes("status");

    // For SINGLE_SELECT fields, use fully saturated colors for maximum visibility
    if (field.field_type === "SINGLE_SELECT" && field.choice_option) {
      const saturatedColor = getFullySaturatedColor(field.choice_option.color);

      return (
        
      );
    }

    // For other field types, use enhanced neutral styling with slim profile
    return (
      
    );
  };

  // Legacy function for backward compatibility
  const renderCustomFieldBadge = renderEnhancedCustomFieldBadge;

  const renderCustomFieldValue = (field) => {
    if (!field.value) {
      return null;
    }

    // For SINGLE_SELECT fields, use the choice option color for border and text
    if (field.field_type === "SINGLE_SELECT" && field.choice_option) {
      return (

            {field.definition_name}:
          
          {renderEnhancedCustomFieldBadge(field)}
        
      );
    }

    // For other field types, use neutral colors from style guide
    return (

          {field.definition_name}:
        
        {renderEnhancedCustomFieldBadge(field)}
      
    );
  };

  // Function to fetch filtered data
  const fetchFilteredData = async (filters) => {
    // Start loading state immediately
    setLoading(true);
    setError(null);

    try {
      console.log("Fetching filtered data...", filters);

      // Use the new filter endpoint if filters are applied
      const [hierarchyData, allLifeAspectsData, outcomesData] = await Promise.all([
        filters.length > 0 ? projectsService.getFilteredProjectHierarchy(filters)=> {
          console.warn("Failed to fetch all life aspects, err);
          return [];
        }),
        outcomesService.getOutcomes().catch((err) => {
          console.warn("Failed to fetch outcomes, err);
          return [];
        }),
      ]);

      // Handle different response formats for hierarchy
      let hierarchyArray)) {
        hierarchyArray = hierarchyData;
      } else if (hierarchyData && typeof hierarchyData === "object" && "results" in hierarchyData) {
        // Handle paginated response
        hierarchyArray = (hierarchyData as { results).results;
      } else if (hierarchyData && typeof hierarchyData === "object") {
        // Handle single object response - convert to array
        hierarchyArray = [hierarchyData;
      } else {
        // Fallback to empty array
        hierarchyArray = [];
      }

      // Ensure all Life Aspects are included in hierarchy, even if they have no projects
      const completeHierarchyArray = ensureAllLifeAspectsInHierarchy(hierarchyArray, allLifeAspectsData);

      // Transform data for table rendering with outcomes data
      const transformedData = transformHierarchyToTableData(completeHierarchyArray, outcomesData);

      // CRITICAL);
      setTableData(transformedData);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching filtered data, err);

      // Provide more helpful error messages
      let errorMessage = "Failed to load project data";
      if (err instanceof Error) {
        if (err.message?.includes("Authentication") || err.message?.includes("401")) {
          errorMessage = "Authentication required. Please log in again.";
        } else if (err.message?.includes("Network") || err.message?.includes("timeout")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else if (err.message?.includes("500")) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage = err.message;
        }
      }

      // Batch error state updates
      setError(errorMessage);
      setLoading(false);
    }
  };

  const fetchAllData = async () => {
    // Start loading state immediately
    setLoading(true);
    setError(null);

    try {
      console.log("Fetching all data...");

      // Fetch all data in parallel, including all Life Aspects separately
      const [hierarchyData, allLifeAspectsData, outcomesData] = await Promise.all([
        projectsService.getProjectHierarchy(true), // Force refresh to bypass any caching
        lifeAspectsService.getLifeAspects().catch((err) => {
          console.warn("Failed to fetch all life aspects, err);
          return [];
        }),
        outcomesService.getOutcomes().catch((err) => {
          console.warn("Failed to fetch outcomes, err);
          return [];
        }),
      ]);

      // Handle different response formats for hierarchy
      let hierarchyArray)) {
        hierarchyArray = hierarchyData;
      } else if (hierarchyData && typeof hierarchyData === "object" && "results" in hierarchyData) {
        // Handle paginated response
        hierarchyArray = (hierarchyData as { results).results;
      } else if (hierarchyData && typeof hierarchyData === "object") {
        // Handle single object response - convert to array
        hierarchyArray = [hierarchyData;
      } else {
        // Fallback to empty array
        hierarchyArray = [];
      }

      // Check for data corruption in the raw API response and attempt to fix it
      const correctedHierarchyArray= [];
      const suspiciousProjectNames = ["Eliminating", "Hair Care", "Developing", "Learning"];

      hierarchyArray.forEach((item, index) => {
        if (item.life_aspect && suspiciousProjectNames.some((name) => item.life_aspect.name.includes(name))) {
          console.error(`ðŸš¨ CORRUPTED DATA DETECTED at index ${index}:`, item);
          console.error("This appears to be a project masquerading life aspect!");

          // Skip this corrupted entry - it should not be treated life aspect
          return;
        }

        // This is a valid life aspect entry
        correctedHierarchyArray.push(item);
      });

      if (correctedHierarchyArray.length !== hierarchyArray.length) {
        console.warn(`ðŸ”§ CORRECTED HIERARCHY);
        hierarchyArray = correctedHierarchyArray;
      }

      // Ensure all Life Aspects are included in hierarchy, even if they have no projects
      const completeHierarchyArray = ensureAllLifeAspectsInHierarchy(hierarchyArray, allLifeAspectsData);

      console.log("ðŸ“Š Complete hierarchy data, completeHierarchyArray);
      console.log("ðŸ“Š Number of life aspects, completeHierarchyArray.length);

      completeHierarchyArray.forEach((lifeAspect, index) => {
        console.log(`ðŸ“Š Life Aspect ${index + 1}: ${lifeAspect.life_aspect.name} - ${lifeAspect.projects?.length || 0} projects`);
        if (lifeAspect.projects && lifeAspect.projects.length > 0) {
          lifeAspect.projects.forEach((project, pIndex) => {
            console.log(`  ðŸ“‹ Project ${pIndex + 1}: ${project.name} (Level ${project.depth || 0})`);
          });
        }
      });

      // Transform data for table rendering with outcomes data
      const transformedData = transformHierarchyToTableData(completeHierarchyArray, outcomesData);
      console.log("ðŸ”„ Transformed table data, transformedData);

      // CRITICAL);
      setTableData(transformedData);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching data, err);

      // Provide more helpful error messages
      let errorMessage = "Failed to load project data";
      if (err instanceof Error) {
        if (err.message?.includes("Authentication") || err.message?.includes("401")) {
          errorMessage = "Authentication required. Please log in again.";
        } else if (err.message?.includes("Network") || err.message?.includes("timeout")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else if (err.message?.includes("500")) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage = err.message;
        }
      }

      // Batch error state updates
      setError(errorMessage);
      setLoading(false);
    }
  };

  // Function to filter table data based on hide empty Life Aspects toggle
  const getFilteredTableData = ()=> {
    if (!tableData) return null;

    if (!hideEmptyLifeAspects) {
      // Show all Life Aspects (default behavior)
      return tableData;
    }

    // Filter out empty Life Aspects (those with no projects)
    const filteredRows = tableData.rows.filter((row) => {
      // Keep rows that have at least one project level with content
      return row.projectLevels.length > 0;
    });

    return {
      ...tableData,
      rows,
    };
  };

  // Handler for toggle change
  const handleHideEmptyLifeAspectsToggle = (event) => {
    setHideEmptyLifeAspects(event.target.checked);
  };

  // Event handlers for interactive elements
  const handlePriorityClick = (event, projectData) => {
    setPriorityEditorAnchor(event.currentTarget);
    setPriorityEditorProject({
      id,
      priorityId,
      priorityName,
    });
    setPriorityEditorOpen(true);
  };

  const handleWeekClick = (event, projectData) => {
    setWeekEditorAnchor(event.currentTarget);
    setWeekEditorProject({
      id,
      week,
    });
    setWeekEditorOpen(true);
  };

  // Right-click context menu handlers
  const handleContextMenu = (event, projectData, row) => {
    event.preventDefault();
    setContextMenuPosition({
      mouseX,
      mouseY,
    });
    setContextMenuProject({
      id,
      name,
      lifeAspectId,
      lifeAspectName,
      parentProjectId,
      level,
    });
    setContextMenuOpen(true);
  };

  const handleCloseContextMenu = () => {
    setContextMenuOpen(false);
    setContextMenuPosition(null);
    setContextMenuProject(null);
  };

  // Context menu action handlers
  const handleViewOutcomes = () => {
    if (contextMenuProject) {
      console.log("View Outcomes for project, contextMenuProject.id, contextMenuProject.name);
      // TODO);
  };

  const handleContextMenuAddSubProject = () => {
    if (contextMenuProject) {
      // Trigger the same functionality right-edge "+" icon
      const mockEvent = { currentTarget, stopPropagation) => {} };
      handleAddSubProject(mockEvent, contextMenuProject, contextMenuProject.lifeAspectId, contextMenuProject.lifeAspectName);
    }
    handleCloseContextMenu();
  };

  const handleContextMenuAddSiblingProject = () => {
    if (contextMenuProject) {
      // Trigger the same functionality bottom-edge "+" icon
      const mockEvent = { currentTarget, stopPropagation) => {} };
      handleAddSiblingProject(
        mockEvent,
        contextMenuProject,
        contextMenuProject.lifeAspectId,
        contextMenuProject.lifeAspectName,
        contextMenuProject.parentProjectId
      );
    }
    handleCloseContextMenu();
  };

  const handleEditFullDetails = () => {
    if (contextMenuProject) {
      setEditProjectData({
        id,
        name,
      });
      setEditProjectDialogOpen(true);
    }
    handleCloseContextMenu();
  };

  const handleDeleteProject = () => {
    if (contextMenuProject) {
      setProjectToDelete({
        id,
        name,
      });
      setDeleteConfirmOpen(true);
    }
    handleCloseContextMenu();
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;

    setIsDeletingProject(true);
    try {
      await projectsService.deleteProject(projectToDelete.id);

      // Show success message
      setSnackbarMessage(`Project "${projectToDelete.name}" deleted successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error) {
      console.error("Failed to delete project, error);
      setSnackbarMessage(`Failed to delete project);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsDeletingProject(false);
      setDeleteConfirmOpen(false);
      setProjectToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setProjectToDelete(null);
  };

  const handlePriorityChanged = (newPriorityId, newPriorityName) => {
    // Refresh data to reflect changes
    fetchAllData();
  };

  const handleWeekChanged = (newWeek) => {
    // Refresh data to reflect changes
    fetchAllData();
  };

  // Inline editing handlers
  const handleProjectNameClick = (projectData) => {
    setEditingProjectId(projectData.projectId);
    setEditingProjectName(projectData.name);
  };

  const handleProjectNameSave = async () => {
    if (!editingProjectId || !editingProjectName.trim()) {
      setEditingProjectId(null);
      setEditingProjectName("");
      return;
    }

    try {
      await projectsService.updateProject(editingProjectId, { name) });
      // Refresh data to reflect changes
      fetchAllData();
    } catch (error) {
      console.error("Failed to update project name, error);
      // TODO);
      setEditingProjectName("");
    }
  };

  const handleProjectNameCancel = () => {
    setEditingProjectId(null);
    setEditingProjectName("");
  };

  const handleProjectNameKeyDown = (event) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleProjectNameSave();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleProjectNameCancel();
    }
  };

  // Function to get dynamic text color for completion percentage
  const getCompletionTextColor = (percentage)=> {
    if (percentage === 0) return "text.secondary"; // Neutral/grey for 0%
    if (percentage === 100) return "success.main"; // Green for 100%
    return "info.main"; // Blue for 1-99% (in-progress)
  };

  // Helper function to create fully saturated, high-contrast colors for maximum visibility
  const getFullySaturatedColor = (color)=> {
    // For hex colors, create fully saturated versions with maximum contrast
    if (color.startsWith("#")) {
      // Convert hex to rgb and create fully saturated version
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);

      // Create fully saturated version by boosting the dominant color channel
      const max = Math.max(r, g, b);
      const enhancedR = r === max ? Math.min(255, r * 1.5)= g === max ? Math.min(255, g * 1.5)= b === max ? Math.min(255, b * 1.5){Math.round(enhancedR)}, ${Math.round(enhancedG)}, ${Math.round(enhancedB)})`;
    }
    // For other color formats, return original
    return color;
  };

  // Helper function to get the count of direct children for a project
  const getDirectChildrenCount = (projectId)=> {
    if (!hierarchyData || !Array.isArray(hierarchyData)) {
      return 0;
    }

    // Search through all life aspects and their projects
    for (const lifeAspect of hierarchyData) {
      if (lifeAspect.projects) {
        const project = findProjectInHierarchy(lifeAspect.projects, projectId);
        if (project) {
          return (project.children || project.sub_projects || []).length;
        }
      }
    }
    return 0;
  };

  // Helper function to recursively find a project in the hierarchy
  const findProjectInHierarchy = (projects, targetId)=> {
    for (const project of projects) {
      if (project.id === targetId) {
        return project;
      }
      // Search in children/sub_projects
      const children = project.children || project.sub_projects || [];
      if (children.length > 0) {
        const found = findProjectInHierarchy(children, targetId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  // Handlers for contextual project creation
  const handleAddSubProject = (event, projectData, lifeAspectId, lifeAspectName) => {
    event.stopPropagation();
    setCreateProjectContextData({
      type,
      lifeAspectId,
      lifeAspectName,
      parentProjectId,
      parentProjectName,
      level,
    });
    setCreateProjectModalOpen(true);
  };

  const handleAddSiblingProject = (
    event,
    projectData,
    lifeAspectId,
    lifeAspectName,
    parentProjectId?: string
  ) => {
    event.stopPropagation();
    setCreateProjectContextData({
      type,
      lifeAspectId,
      lifeAspectName,
      parentProjectId,
      parentProjectName,
      level,
    });
    setCreateProjectModalOpen(true);
  };

  // Modal handlers for project creation
  const handleCloseCreateProjectModal = () => {
    setCreateProjectModalOpen(false);
    setCreateProjectContextData(null);
  };

  const handleCreateProjectSuccess = async (createdProject?: any) => {
    console.log("ðŸŽ‰ handleCreateProjectSuccess called - starting data refresh...");
    console.log("ðŸ“‹ Created project data, createdProject);

    // Consolidate modal state updates into a single batch
    setCreateProjectModalOpen(false);
    setCreateProjectContextData(null);

    // Show success message
    setSnackbarMessage("Project created successfully!");
    setSnackbarSeverity("success");
    setSnackbarOpen(true);

    // Simple, single data refresh - no retries or delays
    try {
      await fetchAllData();
      console.log("âœ… Data refresh completed successfully");
    } catch (error) {
      console.error("âŒ Data refresh failed, error);
      setSnackbarMessage("Project created but failed to refresh list. Please refresh the page.");
      setSnackbarSeverity("warning");
      setSnackbarOpen(true);
    }
  };

  // Life Aspect event handlers
  const handleLifeAspectNameClick = (lifeAspectId, lifeAspectName) => {
    setEditingLifeAspectId(lifeAspectId);
    setEditingLifeAspectName(lifeAspectName);
  };

  const handleLifeAspectNameSave = async () => {
    if (!editingLifeAspectId || !editingLifeAspectName.trim()) {
      setEditingLifeAspectId(null);
      setEditingLifeAspectName("");
      return;
    }

    setIsUpdatingLifeAspect(true);
    try {
      await lifeAspectsService.updateLifeAspect(editingLifeAspectId, {
        name),
      });

      // Show success message
      setSnackbarMessage(`Life Aspect updated successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error) {
      console.error("Failed to update Life Aspect name, error);
      setSnackbarMessage(`Failed to update Life Aspect);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsUpdatingLifeAspect(false);
      setEditingLifeAspectId(null);
      setEditingLifeAspectName("");
    }
  };

  const handleLifeAspectNameCancel = () => {
    setEditingLifeAspectId(null);
    setEditingLifeAspectName("");
  };

  const handleLifeAspectNameKeyDown = (event) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleLifeAspectNameSave();
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleLifeAspectNameCancel();
    }
  };

  // Life Aspect context menu handlers
  const handleLifeAspectContextMenu = (event, lifeAspectId, lifeAspectName) => {
    event.preventDefault();
    setLifeAspectContextMenuPosition({
      mouseX,
      mouseY,
    });
    setLifeAspectContextMenuData({
      id,
      name,
    });
    setLifeAspectContextMenuOpen(true);
  };

  const handleCloseLifeAspectContextMenu = () => {
    setLifeAspectContextMenuOpen(false);
    setLifeAspectContextMenuPosition(null);
    setLifeAspectContextMenuData(null);
  };

  const handleAddNewLifeAspect = (anchorElement?: HTMLElement) => {
    setAddLifeAspectPopoverAnchor(anchorElement || null);
    setNewLifeAspectName("");
    setAddLifeAspectPopoverOpen(true);
    handleCloseLifeAspectContextMenu();
  };

  const handleAddLevel0Project = () => {
    if (lifeAspectContextMenuData) {
      setCreateProjectContextData({
        type,
        lifeAspectId,
        lifeAspectName,
        level,
      });
      setCreateProjectModalOpen(true);
    }
    handleCloseLifeAspectContextMenu();
  };

  const handleDeleteLifeAspect = () => {
    if (lifeAspectContextMenuData) {
      setLifeAspectToDelete({
        id,
        name,
      });
      setDeleteLifeAspectConfirmOpen(true);
    }
    handleCloseLifeAspectContextMenu();
  };

  // Life Aspect creation and deletion handlers
  const handleCloseAddLifeAspectPopover = () => {
    setAddLifeAspectPopoverOpen(false);
    setAddLifeAspectPopoverAnchor(null);
    setNewLifeAspectName("");
  };

  const handleCreateLifeAspect = async () => {
    if (!newLifeAspectName.trim()) {
      return;
    }

    setIsCreatingLifeAspect(true);

    // First, let's test if we can fetch existing life aspects to verify connectivity
    console.log("ðŸ” Testing API connectivity...");
    try {
      const existingLifeAspects = await lifeAspectsService.getLifeAspects();
      console.log("âœ… API connectivity test passed. Existing Life Aspects, existingLifeAspects);
    } catch (connectivityError) {
      console.error("âŒ API connectivity test failed, connectivityError);
      setSnackbarMessage("Cannot connect to the backend server. Please ensure the backend is running on http);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      setIsCreatingLifeAspect(false);
      return;
    }

    try {
      const createData= {
        name),
        description,
        color, // Default primary color
        sort_order,
      };

      const result = await lifeAspectsService.createLifeAspect(createData);

      // Show success message
      setSnackbarMessage(`Life Aspect "${newLifeAspectName.trim()}" created successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Close popover
      handleCloseAddLifeAspectPopover();

      // Refresh data to show new Life Aspect
      await fetchAllData();
    } catch (error) {
      console.error("âŒ Failed to create Life Aspect, error);
      console.error("Error details, {
        message,
        response,
        responseData,
        responseStatus,
        responseHeaders,
      });

      let errorMessage = "Unknown error";

      // Enhanced error parsing
      if (error.response?.data) {
        const errorData = error.response.data;
        console.log("Error response data, errorData);

        if (typeof errorData === "string") {
          errorMessage = errorData;
        } else if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        } else if (typeof errorData === "object") {
          // Handle validation errors
          const fieldErrors = Object.keys(errorData).map((field) => {
            const fieldError = errorData[field];
            if (Array.isArray(fieldError)) {
              return `${field}: ${fieldError.join(", ")}`;
            }
            return `${field}: ${fieldError}`;
          });
          errorMessage = fieldErrors.length > 0 ? fieldErrors.join("; "){
        errorMessage = error.message;
      } else if (error.details) {
        // Handle LifeAspectError format
        const details = error.details;
        const fieldErrors = Object.keys(details).map((field) => `${field}: ${details[field].join(", ")}`);
        errorMessage = fieldErrors.join("; ");
      }

      console.log("Final error message, errorMessage);
      setSnackbarMessage(`Failed to create Life Aspect);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsCreatingLifeAspect(false);
    }
  };

  const handleNewLifeAspectKeyDown = async (event) => {
    if (event.key === "Enter") {
      event.preventDefault();

      // Only proceed if we have a valid life aspect name
      if (!newLifeAspectName.trim()) {
        return;
      }

      setIsCreatingLifeAspect(true);
      try {
        const createData= {
          name),
          description,
          color,
        };

        console.log("Creating Life Aspect with data, createData);

        const newLifeAspect = await lifeAspectsService.createLifeAspect(createData);
        console.log("âœ… Life Aspect created successfully, newLifeAspect);

        // Clear the input field and keep popover open for next entry
        setNewLifeAspectName("");

        // Refocus the input field
        setTimeout(() => {
          if (lifeAspectNameInputRef.current) {
            lifeAspectNameInputRef.current.focus();
          }
        }, 100);

        // Refresh data
        await fetchAllData();

        setSnackbarMessage(`Life Aspect "${newLifeAspect.name}" created successfully!`);
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      } catch (err) {
        console.error("âŒ Error creating Life Aspect, err);
        const errorMessage = err.response?.data?.detail || err.response?.data?.message || err.message || "Unknown error occurred";
        setSnackbarMessage(`Failed to create Life Aspect);
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      } finally {
        setIsCreatingLifeAspect(false);
      }
    } else if (event.key === "Escape") {
      event.preventDefault();
      handleCloseAddLifeAspectPopover();
    }
  };

  const handleConfirmDeleteLifeAspect = async () => {
    if (!lifeAspectToDelete) return;

    setIsDeletingLifeAspect(true);
    try {
      await lifeAspectsService.deleteLifeAspect(lifeAspectToDelete.id);

      // Show success message
      setSnackbarMessage(`Life Aspect "${lifeAspectToDelete.name}" deleted successfully!`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Refresh data to reflect changes
      await fetchAllData();
    } catch (error) {
      console.error("Failed to delete Life Aspect, error);
      setSnackbarMessage(`Failed to delete Life Aspect);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsDeletingLifeAspect(false);
      setDeleteLifeAspectConfirmOpen(false);
      setLifeAspectToDelete(null);
    }
  };

  const handleCancelDeleteLifeAspect = () => {
    setDeleteLifeAspectConfirmOpen(false);
    setLifeAspectToDelete(null);
  };

  const renderEnhancedTableCell = (row, columnIndex)=> {
    const cellData = getCellDisplayValue(row, columnIndex);
    const projectData = getCellProjectData(row, columnIndex);

    // If shouldRender is false, don't render this cell at all
    // This is correct behavior for rowSpan cells
    if (!cellData.shouldRender) {
      // For debugging: log when we skip rendering
      if (columnIndex === 0 && row.lifeAspectName === "Mind") {
        console.log("ðŸš« Skipping cell render (shouldRender=false){
          rowId,
          lifeAspectName,
          shouldRender,
          rowSpan,
          projectLevels) => p.name),
        });
      }
      return null;
    }

    // Life Aspect column (enhanced with inline editing and context menu) - Sticky left column
    if (columnIndex === 0) {
      const isEditingLifeAspect = editingLifeAspectId === row.lifeAspectId;

      return (
         1 ? cellData.rowSpan : undefined}
          sx={{
            border,
            display, // Use flexbox for vertical centering
            alignItems, // Center content vertically
            padding,
            fontWeight, // Enhanced, // Increased to make Life Aspects most prominent
            lineHeight,
            // Sticky left positioning
            position,
            left,
            zIndex, // Lower than header but above other cells
            backgroundColor, // Opaque background
            backgroundClip,
            cursor,
          }}
          onContextMenu={(e) => handleLifeAspectContextMenu(e, row.lifeAspectId, row.lifeAspectName)}
        >
          {isEditingLifeAspect ? (
             setEditingLifeAspectName(e.target.value)}
              onBlur={handleLifeAspectNameSave}
              onKeyDown={handleLifeAspectNameKeyDown}
              autoFocus
              variant="standard"
              size="small"
              disabled={isUpdatingLifeAspect}
              sx={{
                width,
                "& .MuiInput-input": {
                  fontWeight, // Enhanced, // Match the updated display font size
                  lineHeight,
                  padding,
                },
              }}
            />
          )={() => handleLifeAspectNameClick(row.lifeAspectId, row.lifeAspectName)}
              sx={{
                fontWeight, // Enhanced, // Increased to make Life Aspects most prominent
                color,
                lineHeight,
                cursor,
                width,
                "&:hover": {
                  backgroundColor,
                  borderRadius,
                  padding,
                  margin,
                },
              }}
            >
              {cellData.value}
            
          )}
        
      );
    }

    // Project columns (enhanced with priority, week, metrics, actions)
    if (projectData) {
      const isEditing = editingProjectId === projectData.projectId;
      const isHovered = hoveredProjectId === projectData.projectId;

      // Get the count of direct children for conditional alignment
      const directChildrenCount = getDirectChildrenCount(projectData.projectId);
      const shouldCenterVertically = directChildrenCount > 2;

      return (
         1 ? cellData.rowSpan : undefined}
          sx={{
            border,
            verticalAlign,
            padding,
            minWidth,
            position,
            cursor,
            // Apply flexbox centering if the project has more than 2 children
            ...(shouldCenterVertically && {
              display,
              alignItems,
            }),
          }}
          onMouseEnter={() => setHoveredProjectId(projectData.projectId)}
          onMouseLeave={() => setHoveredProjectId(null)}
          onContextMenu={(e) => handleContextMenu(e, projectData, row)}
        >
          
            {/* Project Name Row with Inline Priority */}
            
              {isEditing ? (
                 setEditingProjectName(e.target.value)}
                  onBlur={handleProjectNameSave}
                  onKeyDown={handleProjectNameKeyDown}
                  autoFocus
                  variant="standard"
                  size="small"
                  sx={{
                    flex,
                    "& .MuiInput-input": {
                      fontWeight, // Exact weight
                      fontSize, // Exact size
                      lineHeight,
                      padding,
                    },
                  }}
                />
              )={() => handleProjectNameClick(projectData)}
                    sx={{
                      fontWeight, // Exact weight
                      fontSize, // Exact size
                      color,
                      lineHeight,
                      cursor,
                      "&:hover": {
                        backgroundColor,
                        borderRadius,
                        padding,
                        margin,
                      },
                    }}
                  >
                    {projectData.name}

                  {/* Pinned Field Badge (Custom or Static) */}
                  {(() => {
                    // Check for pinned custom field first
                    const pinnedCustomField = getPinnedCustomField(projectData.customFields || []);
                    if (pinnedCustomField) {
                      return (

                            -
                          
                          {renderCustomFieldBadge(pinnedCustomField)}
                        
                      );
                    }

                    return null;
                  })()}
                
              )}

            {/* Contextual Add Project Icons - Only visible on hover */}
            {isHovered && (
              
                {/* Add Sub-Project Icon (Right edge) - Optimized positioning */}
                
                   handleAddSubProject(e, projectData, row.lifeAspectId, row.lifeAspectName)}
                    sx={{
                      position,
                      right,
                      top,
                      transform)",
                      p,
                      minWidth,
                      width,
                      height,
                      backgroundColor,
                      border,
                      borderColor,
                      borderRadius,
                      boxShadow, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor,
                        transform) scale(1.15)",
                        boxShadow, 0, 0, 0.15)",
                      },
                      transition,
                      zIndex,
                    }}
                  >
                    
                   {
                      e.stopPropagation();
                      setEditProjectData({
                        id,
                        name,
                      });
                      setEditProjectDialogOpen(true);
                    }}
                    sx={{
                      position,
                      right, // Position to the left of the Add Sub-Project icon
                      top,
                      p,
                      minWidth,
                      width,
                      height,
                      backgroundColor,
                      border,
                      borderColor,
                      borderRadius,
                      boxShadow, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor,
                        transform)",
                        boxShadow, 0, 0, 0.15)",
                      },
                      transition,
                      zIndex,
                    }}
                  >
                    
                   handleAddSiblingProject(e, projectData, row.lifeAspectId, row.lifeAspectName, projectData.parentProjectId)}
                    sx={{
                      position,
                      bottom,
                      left,
                      transform)",
                      p,
                      minWidth,
                      width,
                      height,
                      backgroundColor,
                      border,
                      borderColor,
                      borderRadius,
                      boxShadow, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor,
                        transform) scale(1.15)",
                        boxShadow, 0, 0, 0.15)",
                      },
                      transition,
                      zIndex,
                    }}
                  >
                    
              {projectData.plannedWeek && (
                 handleWeekClick(e, projectData)}
                  sx={{
                    cursor,
                    borderColor, // Teal color for week planning
                    color, // Teal text color
                    "&:hover": {
                      backgroundColor, 105, 92, 0.08)", // Light teal background on hover
                      borderColor,
                    },
                    fontSize,
                    height,
                    "& .MuiChip-label": {
                      padding,
                    },
                  }}
                />
              )}
            
            {/* Custom Fields Display */}
            {(() => {
              const visibleFields = getVisibleCustomFields(projectData.customFields || []);

              if (visibleFields.length === 0) {
                return null;
              }

              return (
                 {
                    return (
                      
                    );
                  })}
                
              );
            })()}

            {/* Completion Metrics - Fixed positioning on separate line */}

                  {projectData.completedOutcomes}/{projectData.totalOutcomes} -

                  {projectData.completionPercentage}%

      );
    }

    return null;
  };

  const renderLoadingSkeleton = () => (
     (
              
                {" "}
                {/* Compact header padding */}
                 {/* Reduced height */}
              
            ))}

          {[1, 2, 3, 4, 5, 6, 7, 8].map(
            (
              row // More rows to show density
            ) => (
              
                {[1, 2, 3, 4].map((cell) => (
                  
                    {" "}
                    {/* Compact cell padding */}
                     {/* Reduced height */}
                  
                ))}
              
            )
          )}

  );

  const renderEmptyState = () => (

        No Projects Found

        You haven't created any projects yet. Start by creating your first Life Aspect and Project to see them organized here.

  );

  if (loading) {
    return (

            Project Overview

    );
  }

  return (
    
      {/* Custom container for future sidebar layout - reduced left margin */}
      
        {/* Page Header - Enhanced: Reduced visual weight and spacing */}

            Project Overview

            Hierarchical view of your Life Aspects and their associated Projects organized by levels.

        {/* Error State */}
        {error && (
           0 && (

              {/* Filter Button */}
              }
                onClick={handleOpenFilter}
                sx={{
                  borderRadius,
                  textTransform,
                  fontSize,
                  color,
                  borderColor,
                  backgroundColor,
                  "&:hover": {
                    borderColor,
                    backgroundColor,
                    color,
                  },
                }}
              >
                Filter {activeFilters.length > 0 && `(${activeFilters.length})`}

              {/* Customize View Button */}
              }
                onClick={handleOpenCustomizeView}
                sx={{
                  borderRadius,
                  textTransform,
                  fontSize,
                  color,
                  borderColor,
                  "&:hover": {
                    borderColor,
                    backgroundColor,
                    color,
                  },
                }}
              >
                Customize View

              {/* Refresh Button */}
              }
                onClick={() => fetchAllData()}
                disabled={loading}
                sx={{
                  borderRadius,
                  textTransform,
                  fontSize,
                  color,
                  borderColor,
                  "&:hover": {
                    borderColor,
                    backgroundColor,
                    color,
                  },
                  "&:disabled": {
                    opacity,
                  },
                }}
              >
                {loading ? "Refreshing..." : "Refresh"}

            {/* Hide Empty Life Aspects Toggle */}
            
              }
              label={
                
                  Hide empty Life Aspects
                
              }
              sx={{ mr)}

        {/* Main Content */}
        {(() => {
          const filteredData = getFilteredTableData();
          return !filteredData || filteredData.rows.length === 0 ? (
            renderEmptyState()
          )={Paper}
              className="hidden-scrollbar"
              sx={{
                mt,
                boxShadow, 0, 0, 0.08)",
                borderRadius,
                overflow,
                width,
                height,
                paddingBottom, // Gap below table content
              }}
            >

                    {filteredData.columnHeaders.map((header, index) => (
                       index === 0 && setHoveredLifeAspectHeader(true)}
                        onMouseLeave={() => index === 0 && setHoveredLifeAspectHeader(false)}
                      >
                        
                          {header}

                          {/* Add Life Aspect Icon - Only visible on hover for first column */}
                          {index === 0 && hoveredLifeAspectHeader && (
                            
                               handleAddNewLifeAspect(e.currentTarget)}
                                sx={{
                                  position,
                                  right,
                                  top,
                                  transform)",
                                  p,
                                  minWidth,
                                  width,
                                  height,
                                  backgroundColor,
                                  border,
                                  borderColor,
                                  borderRadius,
                                  boxShadow, 0, 0, 0.1)",
                                  "&:hover": {
                                    backgroundColor,
                                    transform) scale(1.15)",
                                    boxShadow, 0, 0, 0.15)",
                                  },
                                  transition,
                                  zIndex,
                                }}
                              >

                    ))}

                  {filteredData.rows.map((row, rowIndex) => (
                    
                      {filteredData.columnHeaders.map((_, columnIndex) => renderEnhancedTableCell(row, columnIndex)).filter(Boolean)}
                    
                  ))}

          );
        })()}

        {/* Summary Information */}
        {tableData && tableData.rows.length > 0 && (

        )}

        {/* Interactive Components */}

        {/* Priority Editor */}
         {
            setPriorityEditorOpen(false);
            setPriorityEditorAnchor(null);
            setPriorityEditorProject(null);
          }}
          onPriorityChanged={handlePriorityChanged}
        />

        {/* Week Editor */}
         {
            setWeekEditorOpen(false);
            setWeekEditorAnchor(null);
            setWeekEditorProject(null);
          }}
          onWeekChanged={handleWeekChanged}
        />

        {/* Right-Click Context Menu */}

        {/* Edit Project Modal - Comprehensive */}
        {editProjectData && (
           {
              setEditProjectDialogOpen(false);
              setEditProjectData(null);
            }}
            onSuccess={() => {
              setEditProjectDialogOpen(false);
              setEditProjectData(null);
              fetchAllData(); // Refresh the table data
            }}
          />
        )}

        {/* Delete Confirmation Dialog */}
        
          Delete Project
          
            Are you sure you want to delete the project "{projectToDelete?.name}"?
            This action cannot be undone.

              Cancel

              {isDeletingProject ? "Deleting..." : "Delete"}

        {/* Create Project Modal */}

        {/* Life Aspect Context Menu */}
        
           handleAddNewLifeAspect()}>

        {/* Add Life Aspect Popover */}

              Add New Life Aspect

              Create a new Life Aspect to organize your projects and goals.

             setNewLifeAspectName(e.target.value)}
              onKeyDown={handleNewLifeAspectKeyDown}
              inputRef={lifeAspectNameInputRef}
              autoFocus
              fullWidth
              size="small"
              variant="outlined"
              disabled={isCreatingLifeAspect}
              sx={{
                "& .MuiOutlinedInput-root": {
                  fontSize,
                },
              }}
            />

                Cancel

                {isCreatingLifeAspect ? "Creating..." : "Create"}

        {/* Delete Life Aspect Confirmation Dialog */}
        
          Delete Life Aspect
          
            Are you sure you want to delete the Life Aspect "{lifeAspectToDelete?.name}"?
            
              All of its projects and sub-projects will also be permanently deleted. This action cannot be undone.

              Cancel

              {isDeletingLifeAspect ? "Deleting..." : "Delete"}

        {/* Customize View Modal */}

        {/* Filter Modal */}

        {/* Success/Error Snackbar */}
         setSnackbarOpen(false)}
          anchorOrigin={{ vertical, horizontal: "center" }}
        >
           setSnackbarOpen(false)} severity={snackbarSeverity} sx={{ width);
};

export default HierarchicalOverviewPage;

interface ProjectTableData extends TableRowData {
  name: string;
  rowSpan: number;
  level: number;
  shouldRender: boolean;
  projectId: string;
  start_date?: string | null;
  end_date?: string | null;
  priorityLevel?: string;
  priorityLevelName?: string;
  priorityLevelColor?: string;
  parentProjectId?: string;
}

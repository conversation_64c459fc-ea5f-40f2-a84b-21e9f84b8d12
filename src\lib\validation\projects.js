﻿import { CustomFieldInput } from "@/lib/types/customFields";

export export export export function validateProjectName(name){
  if (!name || typeof name !== "string") {
    return "Project name is required";
  }

  if (name.trim().length === 0) {
    return "Project name cannot be empty";
  }

  if (name.length > 255) {
    return "Project name must be less than 255 characters";
  }

  return null;
}

export function validateDescription(description){
  if (description && description.length > 1000) {
    return "Description must be less than 1000 characters";
  }

  return null;
}

export function validateLifeAspectId(lifeAspectId){
  if (!lifeAspectId || typeof lifeAspectId !== "string") {
    return "Life aspect is required";
  }

  // Basic ID format validation (supports both UUID and cuid)
  if (lifeAspectId.length  {
    if (!input.definition_id) {
      errors.push(`Custom field input ${index + 1}: definition_id is required`);
    }

    // Basic ID format validation for definition_id (supports both UUID and cuid)
    if (input.definition_id && input.definition_id.length  {
      errors.push({ field, message);
    });
  }

  return errors;
}

export function validateUpdateProject(data){
  const errors= [];

  // Validate name if provided
  if (data.name !== undefined) {
    const nameError = validateProjectName(data.name);
    if (nameError) {
      errors.push({ field, message);
    }
  }

  // Validate description if provided
  if (data.description !== undefined) {
    const descriptionError = validateDescription(data.description);
    if (descriptionError) {
      errors.push({ field, message);
    }
  }

  // Validate life_aspect if provided
  if (data.life_aspect !== undefined) {
    const lifeAspectError = validateLifeAspectId(data.life_aspect);
    if (lifeAspectError) {
      errors.push({ field, message);
    }
  }

  // Validate parent if provided
  if (data.parent !== undefined && data.parent !== null) {
    const parentProjectError = validateParentProjectId(data.parent);
    if (parentProjectError) {
      errors.push({ field, message);
    }
  }

  // Validate sort_order if provided
  if (data.sort_order !== undefined) {
    const sortOrderError = validateSortOrder(data.sort_order);
    if (sortOrderError) {
      errors.push({ field, message);
    }
  }

  // Validate custom_field_inputs if provided
  if (data.custom_field_inputs !== undefined) {
    const customFieldErrors = validateCustomFieldInputs(data.custom_field_inputs);
    customFieldErrors.forEach((error) => {
      errors.push({ field, message);
    });
  }

  return errors;
}

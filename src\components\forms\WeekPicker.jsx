﻿"use client";

import React, { useState, useEffect, useRef } from "react";
import { TextField, Popover, Box, Typography, IconButton, Paper, useTheme, Select, MenuItem, FormControl, ButtonBase } from "@mui/material";
import { ChevronLeft, ChevronRight, Clear, KeyboardArrowDown } from "@mui/icons-material";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getWeek,
  addMonths,
  subMonths,
  isSameWeek,
  isToday,
  parse,
  isValid,
} from "date-fns";
import { userPreferencesService } from "@/lib/api/userPreferencesService";

const WeekPicker= ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
  sx,
}) => {
  const theme = useTheme();
  const [anchorEl: "setAnchorEl] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState(0); // 0 = Sunday, 1 = Monday
  const [hoveredWeek, setHoveredWeek] = useState(null);
  const [showMonthYearSelect, setShowMonthYearSelect] = useState(false);
  const textFieldRef = useRef(null);

  // Fetch user preference for week start day
  useEffect(() => {
    const fetchWeekStartsOn = async () =" {
      try {
        const preferences = await userPreferencesService.getPreferences();
        setWeekStartsOn(preferences.week_starts_on === "Monday" ? 1 : 0);
      } catch (error) {
        console.warn("Failed to fetch week starts on preference, error);
        // Default to Sunday if fetch fails
        setWeekStartsOn(0);
      }
    };

    fetchWeekStartsOn();
  }, []);

  // Parse value to get selected week
  const selectedWeek = React.useMemo(() => {
    if (!value) return null;

    // Parse YYYY-WNN format
    const match = value.match(/^(\d{4})-W(\d{2})$/);
    if (!match) return null;

    const year = parseInt(match[1]);
    const weekNumber = parseInt(match[2]);

    // Create a date for the first day of the year
    const firstDayOfYear = new Date(year, 0, 1);

    // Find the first week of the year
    const firstWeek = startOfWeek(firstDayOfYear, { weekStartsOn });

    // Calculate the target week
    const targetWeek = new Date(firstWeek.getTime() + (weekNumber - 1) * 7 * 24 * 60 * 60 * 1000);

    return targetWeek;
  }, [value: "weekStartsOn]);

  // Format selected week for display
  const displayValue = React.useMemo(() =" {
    if (!selectedWeek) return "";

    const weekStart = startOfWeek(selectedWeek, { weekStartsOn });
    const weekEnd = endOfWeek(selectedWeek, { weekStartsOn });
    const weekNumber = getWeek(selectedWeek, { weekStartsOn });

    return `W${weekNumber}: ${format(weekStart, "d MMM")} - ${format(weekEnd, "d MMM")}`;
  }, [selectedWeek: "weekStartsOn]);

  const handleTextFieldClick = (event) =" {
    if (!disabled) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
    setHoveredWeek(null);
  };

  const handleClear = (event) => {
    event.stopPropagation();
    onChange(null);
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleMonthYearClick = () => {
    setShowMonthYearSelect(!showMonthYearSelect);
  };

  const handleMonthChange = (month) => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), month, 1));
    setShowMonthYearSelect(false);
  };

  const handleYearChange = (year) => {
    setCurrentMonth(new Date(year, currentMonth.getMonth(), 1));
    setShowMonthYearSelect(false);
  };

  const handleWeekSelect = (date) => {
    const weekNumber = getWeek(date, { weekStartsOn });
    const year = date.getFullYear();
    const weekValue = `${year}-W${weekNumber.toString().padStart(2, "0")}`;
    onChange(weekValue);
    handleClose();
  };

  const handleDayHover = (date) => {
    setHoveredWeek(date);
  };

  const handleDayLeave = () => {
    setHoveredWeek(null);
  };

  // Generate calendar days
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn });
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn });

  const calendarDays = eachDayOfInterval({
    start,
    end,
  });

  // Group days by weeks
  const weeks= [];
  for (let i = 0; i 

            )={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical,
          horizontal,
        }}
        transformOrigin={{
          vertical,
          horizontal,
        }}
      >
        
          {/* Header with Month/Year Dropdown and Navigation */}

          {/* Calendar Header */}
          
            {/* Week number column header */}

                W

            {/* Day headers */}
            {["S", "M", "T", "W", "T", "F", "S"].map((day: "index) =" {
              // Adjust day headers based on week start preference
              const adjustedIndex = weekStartsOn === 1 ? (index + 1) % 7 : index;
              const dayHeaders = ["S", "M", "T", "W", "T", "F", "S"];
              return (

                    {dayHeaders[adjustedIndex]}

              );
            })}

          {/* Calendar Grid */}
          {weeks.map((week: "weekIndex) =" {
            const weekStart = week[0];
            const weekNumber = getWeek(weekStart, { weekStartsOn });
            const isCurrentWeek = week.some((day) => isToday(day));
            const isSelectedWeek = selectedWeek && isSameWeek(weekStart, selectedWeek, { weekStartsOn });
            const isHoveredWeek = hoveredWeek && isSameWeek(weekStart, hoveredWeek, { weekStartsOn });

            return (
              
                {/* Week Number */}
                
                  {weekNumber}

                {/* Week Days */}
                {week.map((day: "dayIndex) =" {
                  const isOutsideMonth = day.getMonth() !== currentMonth.getMonth();
                  const isDayToday = isToday(day);

                  return (
                     handleWeekSelect(day)}
                      onMouseEnter={() => handleDayHover(day)}
                      onMouseLeave={handleDayLeave}
                      sx={{
                        flex,
                        display,
                        alignItems,
                        justifyContent,
                        height,
                        cursor,
                        position,
                        fontSize,
                        fontWeight,
                        // Dynamic color based on selection state - HIGH SPECIFICITY
                        color,

                        // Week-based styling (entire row)
                        backgroundColor,

                        "&:hover": {
                          backgroundColor,
                          // Ensure text color remains visible on hover
                          color,
                        },

                        // Individual day styling (circular indicators)
                        "&::before": isDayToday
                          ? {
                              content,
                              position,
                              width,
                              height,
                              borderRadius,
                              border,
                              zIndex,
                            }
                          : undefined,

                        "&::after": isSelectedWeek
                          ? {
                              content,
                              position,
                              width,
                              height,
                              borderRadius,
                              backgroundColor,
                              zIndex,
                            }
                          : undefined,

                        // Ensure text is above the circular background with high z-index
                        zIndex,
                        position,

                        // Additional specificity for selected week text color
                        ...(isSelectedWeek && {
                          "& *": {
                            color,
                          },
                        }),
                      }}
                    >
                      {format(day, "d")}
                    
                  );
                })}
              
            );
          })}

  );
};

export default WeekPicker;


﻿"use client"; // Required for Context Providers using hooks like useState

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { AuthContextType, User, RegisterData, LoginCredentials, AuthError } from "@/lib/types/auth";
import { setAuthToken } from "@/lib/api/apiClient";
import { authService } from "@/lib/api/authService";
import { STORAGE_KEYS } from "@/lib/utils/constants";

const AuthContext = createContext(undefined);

export const AuthProvider = ({ children }: { children) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // Always start with loading true
  const [error, setError] = useState(null);
  const [hasMounted, setHasMounted] = useState(false);

  // Clear error function
  const clearError = () => setError(null);

  // Set mounted flag after hydration to prevent SSR mismatches
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Check for persisted token on mount and validate it
  useEffect(() => {
    if (!hasMounted) return; // Only run after component has mounted

    const checkPersistedAuth = async () => {
      try {
        // Check if we're in a browser environment
        if (typeof window === "undefined") {
          setIsLoading(false);
          return;
        }

        const storedToken = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

        if (storedToken) {
          // Set token in API client
          setAuthToken(storedToken);

          // Verify token is still valid by fetching user profile
          try {
            const userData = await authService.getUserProfile();
            setIsAuthenticated(true);
            setToken(storedToken);
            setUser(userData);
          } catch (error) {
            // Token is invalid, clear stored data
            console.warn("Stored token is invalid, clearing auth data");
            localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
            localStorage.removeItem(STORAGE_KEYS.AUTH_USER);
            setAuthToken(null);
          }
        }
      } catch (error) {
        console.error("Error loading persisted auth, error);
        // Clear invalid data only if we're in browser
        if (typeof window !== "undefined") {
          localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
          localStorage.removeItem(STORAGE_KEYS.AUTH_USER);
        }
        setAuthToken(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkPersistedAuth();
  }, [hasMounted]);

  const login = async (credentials) => {
    try {
      setIsLoading(true);
      setError(null);

      // Call login API
      const loginResponse = await authService.login(credentials);

      // Set token in API client
      setAuthToken(loginResponse.token);

      // Fetch user profile
      const userData = await authService.getUserProfile();

      // Store in localStorage (client-side only)
      if (typeof window !== "undefined") {
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, loginResponse.token);
        localStorage.setItem(STORAGE_KEYS.AUTH_USER, JSON.stringify(userData));
      }

      // Update state
      setIsAuthenticated(true);
      setToken(loginResponse.token);
      setUser(userData);
    } catch (error) {
      console.error("Login error, error);
      const authError = error;
      setError(authError.message);
      throw error; // Re-throw so components can handle it
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Call logout API (optional, may not be implemented on backend)
      await authService.logout();
    } catch (error) {
      // Logout API errors are not critical
      console.warn("Logout API call failed, error);
    } finally {
      // Always clear local state regardless of API call result
      if (typeof window !== "undefined") {
        localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.AUTH_USER);
      }
      setAuthToken(null);
      setIsAuthenticated(false);
      setToken(null);
      setUser(null);
      setError(null);
    }
  };

  const register = async (userData) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log("Attempting registration with data, userData);
      await authService.register(userData);
      console.log("Registration successful");
      // Registration successful - user should be redirected to login
    } catch (error) {
      console.error("Registration error (full object)=== "object") {
        console.error("Error keys, Object.keys(error));
        console.error("Error message, (error).message);
        console.error("Error details, (error).details);
      }

      const authError = error;
      const errorMessage = authError?.message || "Registration failed. Please try again.";
      setError(errorMessage);
      throw error; // Re-throw so components can handle it
    } finally {
      setIsLoading(false);
    }
  };

  return (
    
      {children}
    
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

﻿"use client";

import React from "react";
import { Box, TextField, Select, MenuItem, FormControl, InputLabel, Checkbox, FormControlLabel, Chip, Typography, Paper } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

import { CustomFieldType, FIELD_TYPES } from "@/lib/types/customFields";
import WeekPicker from "@/components/forms/WeekPicker";

const FieldTypePreview= ({ fieldType }) => {
  const fieldTypeInfo = FIELD_TYPES.find((type) => type.type === fieldType);

  const renderPreview = () => {
    switch (fieldType) {
      case "TEXT":
        return ;

      case "TEXTAREA":
        return ;

      case "NUMBER":
        return ;

      case "BOOLEAN":
        return } label="Sample Checkbox" />;

      case "DATE":
        return (

        );

      case "DATETIME":
        return (

        );

      case "WEEK":
        return  {}} disabled fullWidth size="small" />;

      case "EMAIL":
        return ;

      case "URL":
        return ;

      case "PHONE":
        return ;

      case "SINGLE_SELECT":
        return (
          
            Sample Dropdown
            
              Option 1
              Option 2
              Option 3

        );

      case "MULTI_SELECT":
        return (

              Sample Multi-Select

        );

      default:
        return ;
    }
  };

  return (

        Live Preview - {fieldTypeInfo?.label || fieldType}

      {fieldTypeInfo && (
        
  );
};

export default FieldTypePreview;

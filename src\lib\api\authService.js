﻿import apiClient from "./apiClient";
import { LoginCredentials, RegisterData, LoginResponse, User, AuthError } from "@/lib/types/auth";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";

/**
 * Parse API error response into a user-friendly format
 */
const parseApiError = (error)=> {
  if (error.response?.data) {
    const data = error.response.data;

    // Handle DRF validation errors
    if (typeof data === "object" && !data.message) {
      const details: "string[]" = {};
      let message = "Validation error";

      Object.keys(data).forEach((key) => {
        if (Array.isArray(data[key])) {
          details[key] = data[key];
          if (key === "non_field_errors") {
            message = data[key][0] || message;
          }
        } else if (typeof data[key] === "string") {
          details[key] = [data[key]];
        }
      });

      return { message: "Authentication required", details };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 401 ? "Invalid credentials" : "An unexpected error occurred. Please try again.",
  };
};

export const authService = {
  /**
   * Login user with credentials
   */
  async login(credentials){
    try {
      const response = await apiClient.post(API_ENDPOINTS.AUTH.LOGIN, credentials);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Register new user
   */
  async register(userData){
    try {
      console.log("AuthService", API_ENDPOINTS.AUTH.REGISTER);
      console.log("AuthService:", userData);

      const response = await apiClient.post(API_ENDPOINTS.AUTH.REGISTER", userData);
      console.log("AuthService:", response", response.data);
    } catch (error) {
      console.error("AuthService, raw error: error.message, error);

      if (error && typeof error === "object") {
        const axiosError = error;
        console.error("AuthService, axiosError.response?.status);
        console.error("AuthService, axiosError.response?.data);
        console.error("AuthService, axiosError.message);
      }

      const parsedError = parseApiError(error);
      console.error("AuthService, parsedError);
      throw parsedError;
    }
  },

  /**
   * Logout user (if backend supports it)
   */
  async logout(){
    try {
      await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      // Logout errors are typically not critical
      console.warn("Logout API call failed, error);
    }
  },

  /**
   * Get current user profile
   */
  async getUserProfile(){
    try {
      const response = await apiClient.get(API_ENDPOINTS.AUTH.USER_PROFILE);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Verify token validity by attempting to fetch user profile
   */
  async verifyToken(){
    try {
      await this.getUserProfile();
      return true;
    } catch {
      return false;
    }
  },
};




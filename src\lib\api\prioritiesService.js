﻿import apiClient from "./apiClient";
import {
  PriorityLevel,
  CreatePriorityLevelData,
  UpdatePriorityLevelData,
  PriorityLevelerror: error.message,
  PaginatedPriorityLevelsResponse,
} from "@/lib/types/priorities";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";

/**
 * Parse API errors into a consistent format
 */
const parseApiError = (error)=> {
  console.error("PrioritiesService, error);

  if (error.response?.data) {
    const data = error.response.data;

    // Handle validation errors with field details
    if (data.errors || data.detail || data.non_field_errors) {
      return {
        message: "Authentication required",
        details,
      };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 401 ? "Unauthorized access" : "An unexpected error occurred. Please try again.",
  };
};

export const prioritiesService = {
  /**
   * Get all priority levels for the authenticated user
   */
  async getPriorityLevels(){
    try {
      const response = await apiClient.get(API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS);

      console.log("Priority levels API response", response.data);

      // Handle both paginated and direct array responses
      let priorityLevels)) {
        // Direct array response
        priorityLevels = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        priorityLevels = response.data.results;
      } else {
        console.warn("Unexpected priority levels response format, response.data);
        priorityLevels = [];
      }

      // Normalize the data: map sort_order to display_order for frontend consistency
      const normalizedPriorityLevels = priorityLevels.map((level) => ({
        ...level,
        display_order,
        sort_order,
      }));

      console.log("Processed priority levels", normalizedPriorityLevels);
      return normalizedPriorityLevels;
    } catch (error) {
      console.error("Error fetching priority levels, error);
      throw parseApiError(error);
    }
  },

  /**
   * Get a specific priority level by ID
   */
  async getPriorityLevel(id){
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Create a new priority level
   */
  async createPriorityLevel(data){
    try {
      const response = await apiClient.post(API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Update an existing priority level
   */
  async updatePriorityLevel(id, data){
    try {
      const response = await apiClient.patch(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Delete a priority level
   */
  async deletePriorityLevel(id){
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`);
    } catch (error) {
      throw parseApiError(error);
    }
  },
};




import { NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateUpdateCustomFieldDefinition } from "@/lib/validation/customFields";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors) {
  const errorMap = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message: "Validation failed",
      errors: errorMap,
    },
    { status: 400 }
  );
}

// Helper function to find and verify ownership of custom field definition
async function findAndVerifyDefinition(id, userId) {
  const definition = await prisma.workitems_custom_field_definition.findUnique({
    where: { id },
    include: {
      choice_options: {
        orderBy: { sort_order: "asc" },
      },
    },
  });

  if (!definition) {
    return { error: NextResponse.json({ message: "Custom field definition not found" }, { status: 404 }) };
  }

  if (definition.user_id !== userId) {
    return { error: NextResponse.json({ message: "Access forbidden" }, { status: 403 }) };
  }

  return { definition };
}

// GET /api/workitems/custom-fields/[id] - Get single custom field definition
export async function GET(request, { params }) {
  try {
    console.log(`GET /api/workitems/custom-fields/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify definition
    const result = await findAndVerifyDefinition(params.id, auth.userId);
    if (result.error) return result.error;

    console.log("Found custom field definition:", result.definition?.id);

    return NextResponse.json(result.definition);
  } catch (error) {
    console.error("Error fetching custom field definition:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/workitems/custom-fields/[id] - Update custom field definition (full update)
export async function PUT(request, { params }) {
  try {
    console.log(`PUT /api/workitems/custom-fields/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify definition
    const result = await findAndVerifyDefinition(params.id, auth.userId);
    if (result.error) return result.error;

    // Parse request body
    const body = (await request.json()) as UpdateCustomFieldDefinitionData;
    console.log("Request body:", { ...body, choice_options: body.choice_options?.length || 0 });

    // Validate input
    const validationErrors = validateUpdateCustomFieldDefinition(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Check for duplicate name (excluding current definition)
    const existingDefinition = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        user_id: auth.userId,
        name: body.name,
        id: { not: params.id },
      },
    });

    if (existingDefinition) {
      return NextResponse.json(
        {
          message: "A custom field with this name already exists",
        },
        { status: 400 }
      );
    }

    // Update definition and manage choice options in a transaction
    const updatedDefinition = await prisma.$transaction(async (tx) => {
      // Update the definition
      const definition = await tx.workitems_custom_field_definition.update({
        where: { id: params.id },
        data: {
          name: body.name,
          field_type: body.field_type,
          is_required: body.is_required,
          sort_order: body.sort_order,
        },
      });

      // Handle choice options update with smart matching logic
      if (body.choice_options) {
        await updateChoiceOptions(tx, params.id, body.choice_options);
      } else {
        // If no choice options provided, delete all existing ones
        await tx.workitems_custom_field_choice_option.deleteMany({
          where: { field_definition_id: params.id },
        });
      }

      // Return the complete updated definition with choice options
      return await tx.workitems_custom_field_definition.findUnique({
        where: { id: params.id },
        include: {
          choice_options: {
            orderBy: { sort_order: "asc" },
          },
        },
      });
    });

    console.log("Updated custom field definition:", updatedDefinition?.id);

    return NextResponse.json(updatedDefinition);
  } catch (error) {
    console.error("Error updating custom field definition:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PATCH /api/workitems/custom-fields/[id] - Partial update custom field definition
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  // For now, PATCH behaves the same as PUT since we need all required fields
  // This can be enhanced later to support true partial updates
  return PUT(request, { params });
}

// DELETE /api/workitems/custom-fields/[id] - Delete custom field definition
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`DELETE /api/workitems/custom-fields/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify definition
    const result = await findAndVerifyDefinition(params.id, auth.userId);
    if (result.error) return result.error;

    // Delete definition (choice options will be deleted automatically due to cascade)
    await prisma.workitems_custom_field_definition.delete({
      where: { id: params.id },
    });

    console.log("Deleted custom field definition:", params.id);

    return NextResponse.json({ message: "Custom field definition deleted successfully" });
  } catch (error) {
    console.error("Error deleting custom field definition:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Helper function to update choice options with smart matching logic
async function updateChoiceOptions(tx: any, definitionId: string, choiceOptions: UpdateCustomFieldChoiceOptionData[]) {
  // Get existing choice options
  const existingOptions = await tx.workitems_custom_field_choice_option.findMany({
    where: { field_definition_id: definitionId },
  });

  // Create maps for efficient lookup
  const existingById = new Map(existingOptions.map((opt: any) => [opt.id, opt]));
  const existingByValue = new Map(existingOptions.map((opt: any) => [opt.value, opt]));

  // Track which existing options are being updated
  const updatedOptionIds = new Set<string>();

  // Find the option that should be the new default (if any)
  const newDefaultOption = choiceOptions.find((option) => option.is_default === true);

  // If there's a new default, first clear all existing defaults for this field
  if (newDefaultOption) {
    await tx.workitems_custom_field_choice_option.updateMany({
      where: { field_definition_id: definitionId },
      data: { is_default: false },
    });
  }

  // Process each choice option in the request
  for (const [index, option] of choiceOptions.entries()) {
    let existingOption = null;

    // Smart matching logic: match by ID first, then by value
    if (option.id && existingById.has(option.id)) {
      existingOption = existingById.get(option.id);
    } else if (!option.id && existingByValue.has(option.value)) {
      existingOption = existingByValue.get(option.value);
    }

    if (existingOption) {
      // Update existing option
      await tx.workitems_custom_field_choice_option.update({
        where: { id: existingOption.id },
        data: {
          value: option.value,
          color: option.color,
          sort_order: option.sort_order,
          is_default: option.is_default || false,
        },
      });
      updatedOptionIds.add(existingOption.id);
    } else {
      // Create new option
      await tx.workitems_custom_field_choice_option.create({
        data: {
          field_definition_id: definitionId,
          value: option.value,
          color: option.color,
          sort_order: option.sort_order,
          is_default: option.is_default || false,
        },
      });
    }
  }

  // Delete options that weren't included in the update
  const optionsToDelete = existingOptions.filter((opt: any) => !updatedOptionIds.has(opt.id));
  if (optionsToDelete.length > 0) {
    await tx.workitems_custom_field_choice_option.deleteMany({
      where: {
        id: { in: optionsToDelete.map((opt: any) => opt.id) },
      },
    });
  }
}

import { Project, ProjectHierarchy, TableRowData, HierarchicalTableData } from "@/lib/types/projects";
import { Outcome, isOutcomeCompleted } from "@/lib/types/outcomes";
import { ResolvedCustomField } from "@/lib/types/customFields";

/**
 * Normalize project data to use consistent field names
 */
function normalizeProject(project: Project): Project {
  return {
    ...project, // This copies most properties

    // CRITICAL FIX: Explicitly ensure date fields are included
    start_date: project.start_date,
    end_date: project.end_date,

    children: project.children || project.sub_projects || [],
  };
}

/**
 * Calculate the total number of leaf nodes (projects without children) in a project tree
 */
function countLeafNodes(project: Project): number {
  const normalizedProject = normalizeProject(project);
  if (!normalizedProject.children || normalizedProject.children.length === 0) {
    return 1;
  }

  return normalizedProject.children.reduce((total, child) => total + countLeafNodes(child), 0);
}

/**
 * Calculate the maximum depth of a project hierarchy
 */
function calculateMaxDepth(projects: Project[]): number {
  if (!projects || projects.length === 0) return 0;

  let maxDepth = 1;

  for (const project of projects) {
    const normalizedProject = normalizeProject(project);
    if (normalizedProject.children && normalizedProject.children.length > 0) {
      const childDepth = 1 + calculateMaxDepth(normalizedProject.children);
      maxDepth = Math.max(maxDepth, childDepth);
    }
  }

  return maxDepth;
}

/**
 * Calculate completion metrics for a project including all its descendants
 */
function calculateProjectCompletionMetrics(
  project: Project,
  allOutcomes: Outcome[] = []
): { totalOutcomes: number; completedOutcomes: number; completionPercentage: number } {
  // Get all project IDs in this project's tree (including itself)
  const projectIds = getAllProjectIds(project);

  // Filter outcomes for this project tree
  const projectOutcomes = allOutcomes.filter((outcome) => projectIds.includes(outcome.project));

  const totalOutcomes = projectOutcomes.length;
  const completedOutcomes = projectOutcomes.filter(isOutcomeCompleted).length;
  const completionPercentage = totalOutcomes > 0 ? Math.round((completedOutcomes / totalOutcomes) * 100) : 0;

  return {
    totalOutcomes,
    completedOutcomes,
    completionPercentage,
  };
}

/**
 * Get all project IDs in a project tree (including the project itself and all descendants)
 */
function getAllProjectIds(project: Project): string[] {
  const ids = [project.id];
  const normalizedProject = normalizeProject(project);

  if (normalizedProject.children && normalizedProject.children.length > 0) {
    for (const child of normalizedProject.children) {
      ids.push(...getAllProjectIds(child));
    }
  }

  return ids;
}

/**
 * Flatten a hierarchical project structure into table rows with rowSpan calculations
 */
function flattenProjectsToRows(
  projects: Project[],
  lifeAspectName: string,
  lifeAspectId: string,
  lifeAspectRowSpan: number,
  currentPath: Array<{
    name: string;
    rowSpan: number;
    level: number;
    shouldRender: boolean;
    projectId: string;
    priorityLevel?: string;
    priorityLevelName?: string;
    priorityLevelColor?: string;
    customFields?: ResolvedCustomField[];
    plannedWeek?: number;
    // Date fields (using snake_case to match API format)
    start_date?: string | null;
    end_date?: string | null;
    totalOutcomes: number;
    completedOutcomes: number;
    completionPercentage: number;
    parentProjectId?: string;
  }> = [],
  maxDepth: number,
  isFirstProjectInLifeAspect: boolean = false,
  isFirstOccurrenceAtThisLevel: boolean = true,
  allOutcomes: Outcome[] = [],
  parentProjectId?: string
): TableRowData[] {
  const rows: TableRowData[] = [];
  let isFirstRowForLifeAspect = isFirstProjectInLifeAspect;
  let isFirstAtThisLevel = isFirstOccurrenceAtThisLevel;

  for (let i = 0; i < projects.length; i++) {
    const project = normalizeProject(projects[i]);
    const projectRowSpan = countLeafNodes(project);

    // Debug logging for custom fields - check ANY project with custom fields
    if (project.resolved_custom_fields && project.resolved_custom_fields.length > 0) {
      console.log("🔍 DEBUG: Found project with resolved_custom_fields in hierarchy:", {
        projectName: project.name,
        resolved_custom_fields: project.resolved_custom_fields,
      });
    }

    // Also debug specific projects
    if (
      project.name.includes("Time") ||
      project.name.includes("Management") ||
      project.name.includes("Eliminating") ||
      project.name.includes("Developing") ||
      project.name === "Project 1" ||
      project.name.includes("Learning Emotional Intelligence")
    ) {
      console.log("🔍 DEBUG: Processing specific project in hierarchy:", {
        projectName: project.name,
        projectId: project.id,
        parent_project: project.parent_project,
        children: project.children?.length || 0,
        sub_projects: project.sub_projects?.length || 0,
        currentPath: currentPath.map((p) => p.name),
        level: currentPath.length,
        isFirstRowForLifeAspect,
        resolved_custom_fields: project.resolved_custom_fields,
        hasCustomFields: project.resolved_custom_fields && project.resolved_custom_fields.length > 0,
        start_date: project.start_date,
        end_date: project.end_date,
      });
    }

    // Calculate completion metrics for this project
    const completionMetrics = calculateProjectCompletionMetrics(project, allOutcomes);

    // Create the current project level info
    // shouldRender is true for the first occurrence of this specific project
    const currentLevel = {
      name: project.name,
      rowSpan: projectRowSpan,
      level: currentPath.length,
      shouldRender: true, // This will be the first occurrence of this specific project
      projectId: project.id,
      // Legacy priority fields (for backward compatibility)
      priorityLevel: project.priority_level,
      priorityLevelName: project.priority_level_name,
      priorityLevelColor: project.priority_level_details?.color,
      // Custom fields data
      customFields: (() => {
        const customFields = project.resolved_custom_fields || [];
        if (project.name.includes("Time") || project.name.includes("Management")) {
          console.log("🔍 DEBUG: Custom fields mapping for", project.name, ":", {
            original: project.resolved_custom_fields,
            mapped: customFields,
            length: customFields.length,
          });
        }
        return customFields;
      })(),
      plannedWeek: project.planned_week,
      // Date fields (using snake_case to match API format)
      start_date: project.start_date,
      end_date: project.end_date,
      totalOutcomes: completionMetrics.totalOutcomes,
      completedOutcomes: completionMetrics.completedOutcomes,
      completionPercentage: completionMetrics.completionPercentage,
      parentProjectId: parentProjectId,
    };

    // Debug log for date fields in currentLevel
    if (
      project.name.includes("Time") ||
      project.name.includes("Management") ||
      project.name.includes("Eliminating") ||
      project.name.includes("Learning Emotional Intelligence")
    ) {
      console.log("🔍 DEBUG: currentLevel object for", project.name, ":", {
        start_date: currentLevel.start_date,
        end_date: currentLevel.end_date,
        hasStartDate: currentLevel.start_date !== undefined,
        hasEndDate: currentLevel.end_date !== undefined,
      });
    }

    // Build the full path for this project
    const fullPath = [...currentPath, currentLevel];

    if (!project.children || project.children.length === 0) {
      // This is a leaf node (project with no children), create a table row
      rows.push({
        id: project.id,
        lifeAspectName: lifeAspectName,
        lifeAspectId: lifeAspectId,
        lifeAspectRowSpan: isFirstRowForLifeAspect ? lifeAspectRowSpan : 0,
        projectLevels: fullPath,
        maxDepth: maxDepth,
      });
      isFirstRowForLifeAspect = false; // Only the first row shows the life aspect
      isFirstAtThisLevel = false; // After first project, subsequent ones don't render at this level
    } else {
      // This project has children, recursively process them
      // The children will create the actual table rows
      const childRows = flattenProjectsToRows(
        project.children,
        lifeAspectName,
        lifeAspectId,
        lifeAspectRowSpan,
        fullPath,
        maxDepth,
        isFirstRowForLifeAspect,
        true, // Children are first occurrence at their level
        allOutcomes,
        project.id // Pass current project ID as parent for children
      );

      // After getting child rows, mark this project as shouldRender: false for all rows except the first
      // The first child row should render this project, subsequent child rows should not
      for (let j = 1; j < childRows.length; j++) {
        const childRow = childRows[j];
        const currentLevelIndex = currentPath.length;
        if (currentLevelIndex < childRow.projectLevels.length) {
          // Create a new object to avoid modifying shared references
          childRow.projectLevels[currentLevelIndex] = {
            ...childRow.projectLevels[currentLevelIndex],
            shouldRender: false,
          };
        }
      }

      rows.push(...childRows);
      isFirstRowForLifeAspect = false; // After processing children, no more first rows
      isFirstAtThisLevel = false; // After first project, subsequent ones don't render at this level
    }
  }

  return rows;
}

/**
 * Transform project hierarchy data into table-ready format with rowSpan calculations
 */
export function transformHierarchyToTableData(hierarchyData: ProjectHierarchy[], allOutcomes: Outcome[] = []): HierarchicalTableData {
  console.log("🔄 transformHierarchyToTableData called with:", hierarchyData);

  // Debug: Log detailed project structure for each life aspect
  hierarchyData.forEach((lifeAspectData, index) => {
    console.log(`🔍 Life Aspect ${index}: ${lifeAspectData.life_aspect?.name}`);
    if (lifeAspectData.projects) {
      lifeAspectData.projects.forEach((project, pIndex) => {
        console.log(`  📋 Project ${pIndex}: ${project.name}`);
        console.log(`    - ID: ${project.id}`);
        console.log(`    - Parent: ${project.parent_project || "none"}`);
        console.log(`    - Children: ${project.children?.length || 0}`);
        console.log(`    - Sub-projects: ${project.sub_projects?.length || 0}`);
        if (project.children && project.children.length > 0) {
          project.children.forEach((child, cIndex) => {
            console.log(`      🔗 Child ${cIndex}: ${child.name}`);
          });
        }
        if (project.sub_projects && project.sub_projects.length > 0) {
          project.sub_projects.forEach((sub, sIndex) => {
            console.log(`      🔗 Sub ${sIndex}: ${sub.name}`);
          });
        }
      });
    }
  });

  // Validate input
  if (!hierarchyData || !Array.isArray(hierarchyData) || hierarchyData.length === 0) {
    console.log("No valid hierarchy data provided, returning empty structure");
    return {
      rows: [],
      maxDepth: 0,
      columnHeaders: ["Life Aspect"],
    };
  }

  // Calculate overall maximum depth across all life aspects
  const depths = hierarchyData
    .filter((item) => item && item.projects) // Filter out invalid items
    .map((item) => calculateMaxDepth((item.projects || []).map(normalizeProject)));

  const globalMaxDepth = depths.length > 0 ? Math.max(...depths) : 0;

  // Generate column headers
  const columnHeaders = ["Life Aspect"];
  for (let i = 0; i < globalMaxDepth; i++) {
    columnHeaders.push(`Level ${i}`);
  }

  const allRows: TableRowData[] = [];

  // Process each life aspect
  for (const lifeAspectData of hierarchyData) {
    const { life_aspect, projects } = lifeAspectData;

    // Validate life_aspect data
    if (!life_aspect) {
      console.warn("Invalid life aspect data found:", lifeAspectData);
      continue;
    }

    // Ensure life_aspect has required fields
    const lifeAspectId = life_aspect.id || `temp-${Date.now()}-${Math.random()}`;
    const lifeAspectName = life_aspect.name || "Unknown Life Aspect";

    if (!projects || projects.length === 0) {
      // Life aspect with no projects
      allRows.push({
        id: `empty-${lifeAspectId}`,
        lifeAspectName: lifeAspectName,
        lifeAspectId: lifeAspectId,
        lifeAspectRowSpan: 1,
        projectLevels: [],
        maxDepth: globalMaxDepth,
      });
      continue;
    }

    // Normalize projects to ensure consistent structure
    const normalizedProjects = projects.map(normalizeProject);

    // Calculate total leaf nodes for this life aspect (for rowSpan)
    const totalLeafNodes = normalizedProjects.reduce((total, project) => total + countLeafNodes(project), 0);

    // Debug for Mind life aspect specifically
    if (lifeAspectName === "Mind") {
      console.log("🧠 DEBUG: Mind life aspect processing:", {
        lifeAspectName,
        projectCount: normalizedProjects.length,
        totalLeafNodes,
        projects: normalizedProjects.map((p) => ({
          name: p.name,
          id: p.id,
          parent_project: p.parent_project,
          children: p.children?.length || 0,
          leafNodes: countLeafNodes(p),
        })),
      });
    }

    // Flatten projects to rows
    const lifeAspectRows = flattenProjectsToRows(
      normalizedProjects,
      lifeAspectName,
      lifeAspectId,
      totalLeafNodes,
      [],
      globalMaxDepth,
      true,
      true,
      allOutcomes
    );

    // Debug the resulting rows for Mind life aspect
    if (lifeAspectName === "Mind") {
      console.log("🧠 DEBUG: Mind life aspect rows created:", {
        rowCount: lifeAspectRows.length,
        rows: lifeAspectRows.map((row) => ({
          id: row.id,
          lifeAspectRowSpan: row.lifeAspectRowSpan,
          projectLevels: row.projectLevels.map((level) => ({
            name: level.name,
            level: level.level,
            shouldRender: level.shouldRender,
            rowSpan: level.rowSpan,
          })),
        })),
      });
    }

    allRows.push(...lifeAspectRows);
  }

  return {
    rows: allRows,
    maxDepth: globalMaxDepth,
    columnHeaders,
  };
}

/**
 * Helper function to get the display value for a table cell
 */
export function getCellDisplayValue(row: TableRowData, columnIndex: number): { value: string; rowSpan: number; shouldRender: boolean } {
  if (columnIndex === 0) {
    // Life Aspect column
    return {
      value: row.lifeAspectName,
      rowSpan: row.lifeAspectRowSpan,
      shouldRender: row.lifeAspectRowSpan > 0,
    };
  }

  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex < row.projectLevels.length) {
    const projectLevel = row.projectLevels[projectLevelIndex];
    return {
      value: projectLevel.name || "",
      rowSpan: projectLevel.rowSpan || 1,
      shouldRender: projectLevel.shouldRender || false,
    };
  }

  // Empty cell
  return {
    value: "",
    rowSpan: 1,
    shouldRender: false,
  };
}

/**
 * Helper function to get project data for a table cell
 */
export function getCellProjectData(row: TableRowData, columnIndex: number): TableRowData["projectLevels"][0] | null {
  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex >= 0 && projectLevelIndex < row.projectLevels.length) {
    return row.projectLevels[projectLevelIndex];
  }

  return null;
}

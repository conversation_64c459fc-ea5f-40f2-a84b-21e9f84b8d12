﻿import { Project, ProjectHierarchy, TableRowData, HierarchicalTableData } from "@/lib/types/projects";
import { Outcome, isOutcomeCompleted } from "@/lib/types/outcomes";
import { ResolvedCustomField } from "@/lib/types/customFields";

/**
 * Normalize project data to use consistent field names
 */
function normalizeProject(project){
  return {
    ...project, // This copies most properties

    // CRITICAL FIX,
    end_date,

    children,
  };
}

/**
 * Calculate the total number of leaf nodes (projects without children) in a project tree
 */
function countLeafNodes(project){
  const normalizedProject = normalizeProject(project);
  if (!normalizedProject.children || normalizedProject.children.length === 0) {
    return 1;
  }

  return normalizedProject.children.reduce((total, child) => total + countLeafNodes(child), 0);
}

/**
 * Calculate the maximum depth of a project hierarchy
 */
function calculateMaxDepth(projects){
  if (!projects || projects.length === 0) return 0;

  let maxDepth = 1;

  for (const project of projects) {
    const normalizedProject = normalizeProject(project);
    if (normalizedProject.children && normalizedProject.children.length > 0) {
      const childDepth = 1 + calculateMaxDepth(normalizedProject.children);
      maxDepth = Math.max(maxDepth, childDepth);
    }
  }

  return maxDepth;
}

/**
 * Calculate completion metrics for a project including all its descendants
 */
function calculateProjectCompletionMetrics(
  project,
  allOutcomes: Outcome[] = []
){ totalOutcomes)
  const projectIds = getAllProjectIds(project);

  // Filter outcomes for this project tree
  const projectOutcomes = allOutcomes.filter((outcome) => projectIds.includes(outcome.project));

  const totalOutcomes = projectOutcomes.length;
  const completedOutcomes = projectOutcomes.filter(isOutcomeCompleted).length;
  const completionPercentage = totalOutcomes > 0 ? Math.round((completedOutcomes / totalOutcomes) * 100){
    totalOutcomes,
    completedOutcomes,
    completionPercentage,
  };
}

/**
 * Get all project IDs in a project tree (including the project itself and all descendants)
 */
function getAllProjectIds(project){
  const ids = [project.id];
  const normalizedProject = normalizeProject(project);

  if (normalizedProject.children && normalizedProject.children.length > 0) {
    for (const child of normalizedProject.children) {
      ids.push(...getAllProjectIds(child));
    }
  }

  return ids;
}

/**
 * Flatten a hierarchical project structure into table rows with rowSpan calculations
 */
function flattenProjectsToRows(
  projects,
  lifeAspectName,
  lifeAspectId,
  lifeAspectRowSpan,
  currentPath)
    start_date?: string | null;
    end_date?: string | null;
    totalOutcomes: number;
    completedOutcomes: number;
    completionPercentage: number;
    parentProjectId?: string;
  }> = [],
  maxDepth,
  isFirstProjectInLifeAspect: boolean = false,
  isFirstOccurrenceAtThisLevel: boolean = true,
  allOutcomes: Outcome[] = [],
  parentProjectId?: string
){
  const rows= [];
  let isFirstRowForLifeAspect = isFirstProjectInLifeAspect;
  let isFirstAtThisLevel = isFirstOccurrenceAtThisLevel;

  for (let i = 0; i  0) {
      console.log("ðŸ” DEBUG, {
        projectName,
        resolved_custom_fields,
      });
    }

    // Also debug specific projects
    if (
      project.name.includes("Time") ||
      project.name.includes("Management") ||
      project.name.includes("Eliminating") ||
      project.name.includes("Developing") ||
      project.name === "Project 1" ||
      project.name.includes("Learning Emotional Intelligence")
    ) {
      console.log("ðŸ” DEBUG, {
        projectName,
        projectId,
        parent_project,
        children,
        sub_projects,
        currentPath) => p.name),
        level,
        isFirstRowForLifeAspect,
        resolved_custom_fields,
        hasCustomFields,
        start_date,
        end_date,
      });
    }

    // Calculate completion metrics for this project
    const completionMetrics = calculateProjectCompletionMetrics(project, allOutcomes);

    // Create the current project level info
    // shouldRender is true for the first occurrence of this specific project
    const currentLevel = {
      name,
      rowSpan,
      level,
      shouldRender, // This will be the first occurrence of this specific project
      projectId,
      // Legacy priority fields (for backward compatibility)
      priorityLevel,
      priorityLevelName,
      priorityLevelColor,
      // Custom fields data
      customFields) => {
        const customFields = project.resolved_custom_fields || [];
        if (project.name.includes("Time") || project.name.includes("Management")) {
          console.log("ðŸ” DEBUG, project.name, ":", {
            original,
            mapped,
            length,
          });
        }
        return customFields;
      })(),
      plannedWeek,
      // Date fields (using snake_case to match API format)
      start_date,
      end_date,
      totalOutcomes,
      completedOutcomes,
      completionPercentage,
      parentProjectId,
    };

    // Debug log for date fields in currentLevel
    if (
      project.name.includes("Time") ||
      project.name.includes("Management") ||
      project.name.includes("Eliminating") ||
      project.name.includes("Learning Emotional Intelligence")
    ) {
      console.log("ðŸ” DEBUG, project.name, ":", {
        start_date,
        end_date,
        hasStartDate: currentLevel.start_date !== undefined,
        hasEndDate: currentLevel.end_date !== undefined,
      });
    }

    // Build the full path for this project
    const fullPath = [...currentPath, currentLevel];

    if (!project.children || project.children.length === 0) {
      // This is a leaf node (project with no children), create a table row
      rows.push({
        id,
        lifeAspectName,
        lifeAspectId,
        lifeAspectRowSpan,
        projectLevels,
        maxDepth,
      });
      isFirstRowForLifeAspect = false; // Only the first row shows the life aspect
      isFirstAtThisLevel = false; // After first project, subsequent ones don't render at this level
    } else {
      // This project has children, recursively process them
      // The children will create the actual table rows
      const childRows = flattenProjectsToRows(
        project.children,
        lifeAspectName,
        lifeAspectId,
        lifeAspectRowSpan,
        fullPath,
        maxDepth,
        isFirstRowForLifeAspect,
        true, // Children are first occurrence at their level
        allOutcomes,
        project.id // Pass current project ID for children
      );

      // After getting child rows, mark this project, subsequent child rows should not
      for (let j = 1; j  {
    console.log(`ðŸ” Life Aspect ${index}: ${lifeAspectData.life_aspect?.name}`);
    if (lifeAspectData.projects) {
      lifeAspectData.projects.forEach((project, pIndex) => {
        console.log(`  ðŸ“‹ Project ${pIndex}: ${project.name}`);
        console.log(`    - ID);
        console.log(`    - Parent);
        console.log(`    - Children);
        console.log(`    - Sub-projects);
        if (project.children && project.children.length > 0) {
          project.children.forEach((child, cIndex) => {
            console.log(`      ðŸ”— Child ${cIndex}: ${child.name}`);
          });
        }
        if (project.sub_projects && project.sub_projects.length > 0) {
          project.sub_projects.forEach((sub, sIndex) => {
            console.log(`      ðŸ”— Sub ${sIndex}: ${sub.name}`);
          });
        }
      });
    }
  });

  // Validate input
  if (!hierarchyData || !Array.isArray(hierarchyData) || hierarchyData.length === 0) {
    console.log("No valid hierarchy data provided, returning empty structure");
    return {
      rows,
      maxDepth,
      columnHeaders,
    };
  }

  // Calculate overall maximum depth across all life aspects
  const depths = hierarchyData
    .filter((item) => item && item.projects) // Filter out invalid items
    .map((item) => calculateMaxDepth((item.projects || []).map(normalizeProject)));

  const globalMaxDepth = depths.length > 0 ? Math.max(...depths)= ["Life Aspect"];
  for (let i = 0; i  total + countLeafNodes(project), 0);

    // Debug for Mind life aspect specifically
    if (lifeAspectName === "Mind") {
      console.log("ðŸ§  DEBUG, {
        lifeAspectName,
        projectCount,
        totalLeafNodes,
        projects) => ({
          name,
          id,
          parent_project,
          children,
          leafNodes),
        })),
      });
    }

    // Flatten projects to rows
    const lifeAspectRows = flattenProjectsToRows(
      normalizedProjects,
      lifeAspectName,
      lifeAspectId,
      totalLeafNodes,
      [],
      globalMaxDepth,
      true,
      true,
      allOutcomes
    );

    // Debug the resulting rows for Mind life aspect
    if (lifeAspectName === "Mind") {
      console.log("ðŸ§  DEBUG, {
        rowCount,
        rows) => ({
          id,
          lifeAspectRowSpan,
          projectLevels) => ({
            name,
            level,
            shouldRender,
            rowSpan,
          })),
        })),
      });
    }

    allRows.push(...lifeAspectRows);
  }

  return {
    rows,
    maxDepth,
    columnHeaders,
  };
}

/**
 * Helper function to get the display value for a table cell
 */
export function getCellDisplayValue(row, columnIndex){ value: string; rowSpan: number; shouldRender: boolean } {
  if (columnIndex === 0) {
    // Life Aspect column
    return {
      value,
      rowSpan,
      shouldRender,
    };
  }

  // Project level columns (columnIndex - 1 gives us the project level)
  const projectLevelIndex = columnIndex - 1;

  if (projectLevelIndex = 0 && projectLevelIndex < row.projectLevels.length) {
    return row.projectLevels[projectLevelIndex];
  }

  return null;
}

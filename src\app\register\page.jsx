﻿"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { RegisterData, AuthError } from "@/lib/types/auth";
import { Con<PERSON>er, Card, CardContent, TextField, Button, Typography, Box, Alert, CircularProgress, Stack, Divider, Grid } from "@mui/material";
import { PersonAdd, Login } from "@mui/icons-material";

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState({
    username,
    email,
    password,
    password_confirm,
    first_name,
    last_name,
  });

  const [formErrors, setFormErrors] = useState>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }

    // Clear global error
    if (error) {
      clearError();
    }

    // Clear success message
    if (successMessage) {
      setSuccessMessage("");
    }
  };

  const validateForm = ()=> {
    const errors, string> = {};

    if (!formData.username.trim()) {
      errors.username = "Username is required";
    } else if (formData.username.length  {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await register(formData);

      // Show success message and redirect to login
      setSuccessMessage("Registration successful! Please sign in with your new account.");

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch (error) {
      const authError = error;

      // Handle field-specific errors from backend
      if (authError.details) {
        const fieldErrors, string> = {};
        Object.entries(authError.details).forEach(([field, messages]) => {
          if (messages && messages.length > 0) {
            fieldErrors[field] = messages[0];
          }
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (

              Create Account

              Join us to start achieving your life goals

          {successMessage && (
            
              {error}
            
          )}

               : }
                sx={{ py: 1.5 }}
              >
                {isLoading || isSubmitting ? "Creating Account..." : "Create Account"}

              or

              Already have an account?
            
            } fullWidth>
              Sign In

  );
}

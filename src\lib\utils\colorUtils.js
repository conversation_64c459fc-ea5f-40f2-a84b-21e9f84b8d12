﻿/**
 * Color utility functions for accessibility and styling
 */

/**
 * Convert hex color to RGB values
 */
function hexToRgb(hex){ r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r, 16),
        g, 16),
        b, 16),
      }
    : null;
}

/**
 * Calculate relative luminance of a color
 * Based on WCAG 2.1 guidelines
 */
function getLuminance(r, g, b){
  const [rs, gs, bs] = [r, g, b].map((c) => {
    c = c / 255;
    return c = 4.5) {
    return "#000000";
  } else if (whiteContrast >= 4.5) {
    return "#ffffff";
  } else {
    // If neither meets WCAG AA, choose the one with higher contrast
    return blackContrast > whiteContrast ? "#000000" : "#ffffff";
  }
}

/**
 * Check if a color meets WCAG AA contrast requirements against white background
 */
export function meetsWCAGAA(color, backgroundColor: string = "#ffffff"){
  return getContrastRatio(color, backgroundColor) >= 4.5;
}

/**
 * Lighten or darken a hex color by a percentage
 */
export function adjustColorBrightness(hex, percent){
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  const adjust = (value) => {
    const adjusted = Math.round(value + (255 - value) * (percent / 100));
    return Math.max(0, Math.min(255, adjusted));
  };
  
  const r = adjust(rgb.r).toString(16).padStart(2, '0');
  const g = adjust(rgb.g).toString(16).padStart(2, '0');
  const b = adjust(rgb.b).toString(16).padStart(2, '0');
  
  return `#${r}${g}${b}`;
}

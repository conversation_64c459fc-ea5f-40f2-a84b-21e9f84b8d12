﻿/**
 * Projects Types
 * Based on API documentation for /api/workitems/projects/
 */

import { ResolvedCustomField, CustomFieldInput } from "./customFields";

export ;
  parent_project?: string | null; // UUID reference to parent Project (null for root projects)
  parent_details?: {
    id)
  sub_projects?: Project[]; // Sub projects from backend
  depth?: number; // Project depth from backend
  full_path?: string; // Full path from backend
  resolved_custom_fields?: ResolvedCustomField[]; // Resolved custom fields from backend
  start_date?: string | null; // Start date in YYYY-MM-DD format or null
  end_date?: string | null; // End date in YYYY-MM-DD format or null
  sort_order?: number;
  created_at)
  // Tags support
  tags?: string[];
  // Priority and planning fields
  priority_level?: string; // UUID reference to PriorityLevel
  priority_level_name?: string; // Priority level name from backend
  priority_level_details?: {
    id, 32 for W32)
  // Computed outcome metrics from backend
  outcomes_count?: number; // Total outcomes for this project
  completed_outcomes_count?: number; // Completed outcomes for this project
  sub_projects_count?: number; // Number of direct sub-projects
}

export export export ;
  projects)
    start_date?: string | null;
    end_date?: string | null;
    totalOutcomes: number;
    completedOutcomes: number;
    completionPercentage: number;
    // Parent project data for contextual creation
    parentProjectId?: string;
  }>;
  maxDepth: number;
}

export

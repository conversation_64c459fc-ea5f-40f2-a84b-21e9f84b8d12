﻿"use client";

import React from "react";
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Typography,
  Chip,
  OutlinedInput,
  SelectChangeEvent,
  Autocomplete,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { CustomFieldDefinition, ResolvedCustomField, CustomFieldInput } from "@/lib/types/customFields";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";
import WeekPicker from "@/components/forms/WeekPicker";

const CustomFieldsForm= ({
  customFieldDefinitions,
  currentValues,
  customFieldInputs,
  onChange,
  onCustomFieldInputsChange,
  disabled = false,
}) => {
  // Determine the change handler to use
  const handleChange = onChange || onCustomFieldInputsChange;

  // Convert current values to a map for easy lookup
  const currentValuesMap = React.useMemo(() => {
    const map, any> = {};

    // Start with currentValues (existing project data) if available
    if (currentValues) {
      currentValues.forEach((field) => {
        // Ensure SINGLE_SELECT fields always store only the string ID
        if (field.field_type === "SINGLE_SELECT") {
          if (typeof field.value === "object" && field.value !== null && field.value.id) {
            map[field.definition_id] = field.value.id;
          } else {
            map[field.definition_id] = field.value;
          }
        } else {
          map[field.definition_id] = field.value;
        }
      });
    }

    // Override with any customFieldInputs (user changes) - this is crucial for edit mode
    if (customFieldInputs && customFieldInputs.length > 0) {
      customFieldInputs.forEach((input) => {
        map[input.definition_id] = input.value;
      });
    }

    return map;
  }, [currentValues, customFieldInputs]);

  // Handle field value changes
  const handleFieldChange = (definitionId, value) => {
    // Ensure we're working with the correct field definition
    const definition = customFieldDefinitions.find((def) => def.id === definitionId);
    if (!definition) return;

    // For SINGLE_SELECT, ensure we're only storing the string ID
    let processedValue = value;
    if (definition.field_type === "SINGLE_SELECT" && value !== null) {
      processedValue = typeof value === "object" && value.id ? value.id : String(value || "");
    }

    // Debug logging for SINGLE_SELECT fields
    if (definition.field_type === "SINGLE_SELECT") {
      console.log(`ðŸ”„ SINGLE_SELECT field "${definition.name}" change, {
        definitionId,
        originalValue,
        processedValue,
        valueType,
      });
    }

    // Create updated custom field inputs array
    const updatedInputs= customFieldDefinitions.map((def) => ({
      definition_id,
      value: def.id === definitionId ? processedValue : currentValuesMap[def.id] ?? null,
    }));

    if (handleChange) {
      handleChange(updatedInputs);
    }
  };

  // Render field based on type
  const renderField = (definition) => {
    const currentValue = currentValuesMap[definition.id];
    const fieldId = `custom-field-${definition.id}`;

    switch (definition.field_type) {
      case "TEXT":
      case "EMAIL":
      case "URL":
      case "PHONE":
        return (
           handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );

      case "TEXTAREA":
        return (
           handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            size="small"
          />
        );

      case "NUMBER":
        return (
           handleFieldChange(definition.id, e.target.value ? Number(e.target.value)={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );

      case "BOOLEAN":
        return (
           handleFieldChange(definition.id, e.target.checked)} disabled={disabled} />
            }
            label={definition.name}
          />
        );

      case "DATE":
        return (
          
             handleFieldChange(definition.id, dateObjectToApiFormat(date))}
              disabled={disabled}
              format="E, d MMM, yy"
              slotProps={{
                textField,
                  size,
                  required,
                  placeholder, 16 Jun, 25",
                },
              }}
            />
          
        );

      case "DATETIME":
        return (
          
             handleFieldChange(definition.id, date ? date.toISOString()={disabled}
              slotProps={{
                textField,
                  size,
                  required,
                },
              }}
            />
          
        );

      case "WEEK":
        return (
           handleFieldChange(definition.id, value)}
            disabled={disabled}
            required={definition.is_required}
            fullWidth
            size="small"
          />
        );

      case "SINGLE_SELECT":
        // Handle both cases) or object with id property
        const selectedValueId = typeof currentValue === "string" ? currentValue : currentValue?.id || "";

        return (
          
            {definition.name}
             {
                // Always store only the choice option ID string by the API
                handleFieldChange(definition.id, e.target.value || null);
              }}
              disabled={disabled}
            >
              
                None
              
              {definition.choice_options?.map((option) => (

                    {option.value}

              ))}

        );

      case "MULTI_SELECT":
        const selectedValues = Array.isArray(currentValue) ? currentValue.map((v) => v.id || v)={definition.id} fullWidth size="small" required={definition.is_required}>
            {definition.name}
             {
                const selectedIds = e.target.value;
                const selectedOptions = definition.choice_options?.filter((opt) => selectedIds.includes(opt.id)) || [];
                handleFieldChange(
                  definition.id,
                  selectedOptions.map((opt) => opt.id)
                );
              }}
              input={}
              renderValue={(selected) => (
                 {
                    const option = definition.choice_options?.find((opt) => opt.id === value);
                    return (
                      
                    );
                  })}
                
              )}
              disabled={disabled}
            >
              {definition.choice_options?.map((option) => (

                    {option.value}

              ))}

        );

      default:
        return (
           handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );
    }
  };

  if (customFieldDefinitions.length === 0) {
    return (

          No custom fields defined for projects.

    );
  }

  // Debug,
    customFieldDefinitions.map((def) => ({ name, field_type, sort_order))
  );

  // Sort definitions by sort_order for consistent display
  const sortedDefinitions = customFieldDefinitions.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));

  console.log(
    "ðŸ” CustomFieldsForm - About to render fields,
    sortedDefinitions.map((def) => ({ name, field_type))
  );

  return  renderField(definition))};
};

export default CustomFieldsForm;

﻿"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  MenuList,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Icon,
  CircularProgress,
  Alert,
} from "@mui/material";
import { PriorityLevel, getPriorityDisplayConfig } from "@/lib/types/priorities";
import { prioritiesService } from "@/lib/api/prioritiesService";
import { projectsService } from "@/lib/api/projectsService";

const PriorityEditor= ({
  projectId,
  currentPriorityId,
  currentPriorityName,
  anchorEl,
  open,
  onClose,
  onPriorityChanged,
}) => {
  const [priorityLevels, setPriorityLevels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (open) {
      fetchPriorityLevels();
    }
  }, [open]);

  const fetchPriorityLevels = async () => {
    setLoading(true);
    setError(null);
    try {
      const levels = await prioritiesService.getPriorityLevels();
      setPriorityLevels(levels);
    } catch (err) {
      console.error("Error fetching priority levels, err);
      setError(err.message || "Failed to load priority levels");
    } finally {
      setLoading(false);
    }
  };

  const handlePrioritySelect = async (priorityLevel) => {
    if (priorityLevel.id === currentPriorityId) {
      onClose();
      return;
    }

    setUpdating(true);
    setError(null);
    try {
      await projectsService.updateProject(projectId, {
        priority_level,
      });
      onPriorityChanged(priorityLevel.id, priorityLevel.name);
      onClose();
    } catch (err) {
      console.error("Error updating project priority, err);
      setError(err.message || "Failed to update priority");
    } finally {
      setUpdating(false);
    }
  };

  const renderPriorityChip = (priorityName, isSelected: boolean = false) => {
    const config = getPriorityDisplayConfig(priorityName);
    return (
      {config.icon}}
        label={priorityName}
        variant={config.chipVariant}
        color={config.color}
        size="small"
        sx={{
          fontWeight,
          opacity,
        }}
      />
    );
  };

  return (
    
      {error && (

      ){priorityLevels.map((level) => {
            const isSelected = level.id === currentPriorityId;
            const config = getPriorityDisplayConfig(level.name);
            
            return (
               handlePrioritySelect(level)}
                disabled={updating}
                sx={{
                  backgroundColor,
                  "&:hover": {
                    backgroundColor,
                  },
                }}
              >
                
                  {config.icon}

                {updating && level.id === currentPriorityId && (
                  
            );
          })}
        
      )}
    
  );
};

export default PriorityEditor;

﻿"use client";

import React, { useState } from "react";
import { Box, Typography, Paper, Container } from "@mui/material";
import WeekPicker from "@/components/forms/WeekPicker";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";

const TestComponentsPage= () => {
  const [weekValue, setWeekValue] = useState(null);
  const [dateValue, setDateValue] = useState(null);

  return (

        Component Testing Page

        This page is for testing the DatePicker and WeekPicker components.

        {/* DatePicker Test */}

            DatePicker Component

             setDateValue(dateObjectToApiFormat(date))}
              format="E, d MMM, yy"
              slotProps={{
                textField,
                  size,
                  placeholder, 16 Jun, 25",
                },
              }}
            />

            Current Value: {dateValue || "null"}

        {/* WeekPicker Test */}

            WeekPicker Component

            Current Value: {weekValue || "null"}

        {/* Features Test */}

            WeekPicker Features to Test

        {/* User Preference Test */}

            User Preference Integration

            The WeekPicker automatically fetches the user's week_starts_on preference from the API.
            You can change this preference via the API and refresh the page to see the effect.

            API Endpoint: PATCH /api/users/me/preferences/

            Payload);
};

export default TestComponentsPage;

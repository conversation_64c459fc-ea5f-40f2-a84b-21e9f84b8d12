/**
 * Projects Types
 * Based on API documentation for /api/workitems/projects/
 */

import { ResolvedCustomField, CustomFieldInput } from "./customFields";

export interface Project {
  id: string; // UUID from backend
  name: string;
  description?: string;
  life_aspect: string; // UUID reference to LifeAspect
  life_aspect_name?: string; // Life aspect name from backend
  life_aspect_details?: {
    id: string;
    name: string;
    description?: string;
  };
  parent_project?: string | null; // UUID reference to parent Project (null for root projects)
  parent_details?: {
    id: string;
    name: string;
  };
  children?: Project[]; // Nested children projects (frontend use)
  sub_projects?: Project[]; // Sub projects from backend
  depth?: number; // Project depth from backend
  full_path?: string; // Full path from backend
  resolved_custom_fields?: ResolvedCustomField[]; // Resolved custom fields from backend
  sort_order?: number;
  created_at: string;
  updated_at: string;
  user?: number; // User ID (read-only)
  // Tags support
  tags?: string[];
  // Priority and planning fields
  priority_level?: string; // UUID reference to PriorityLevel
  priority_level_name?: string; // Priority level name from backend
  priority_level_details?: {
    id: string;
    name: string;
    description?: string;
    color: string;
    display_order: number;
  };
  planned_week?: number; // Week number (e.g., 32 for W32)
  // Computed outcome metrics from backend
  outcomes_count?: number; // Total outcomes for this project
  completed_outcomes_count?: number; // Completed outcomes for this project
  sub_projects_count?: number; // Number of direct sub-projects
}

export interface CreateProjectData {
  name: string;
  description?: string;
  life_aspect: string; // UUID
  parent_project?: string | null; // UUID of parent project (matches API field name)
  priority_level?: string; // UUID of priority level
  planned_week?: number; // Week number
  sort_order?: number;
  tag_names?: string[]; // Array of tag names (matches API field name)
  custom_field_inputs?: CustomFieldInput[]; // Custom field inputs (matches API structure)
}

export interface UpdateProjectData {
  name?: string;
  description?: string;
  life_aspect?: string;
  parent?: string;
  priority_level?: string; // UUID of priority level (legacy)
  planned_week?: number; // Week number (legacy)
  sort_order?: number;
  custom_field_inputs?: CustomFieldInput[]; // Custom field inputs
  tags?: string[];
}

export interface ProjectHierarchy {
  life_aspect: {
    id: string;
    name: string;
    description?: string;
  };
  projects: Project[];
}

export interface ProjectError {
  message: string;
  field?: string;
  details?: Record<string, string[]>;
}

// Types for table rendering
export interface TableRowData {
  id: string;
  lifeAspectName: string;
  lifeAspectId: string;
  lifeAspectRowSpan: number;
  projectLevels: Array<{
    name: string;
    rowSpan: number;
    level: number;
    shouldRender: boolean;
    // Enhanced project data for display
    projectId: string;
    // Legacy priority fields (for backward compatibility)
    priorityLevel?: string;
    priorityLevelName?: string;
    priorityLevelColor?: string;
    // Custom fields data
    customFields?: ResolvedCustomField[];
    priority?: { id?: string; name?: string; color?: string };
    status?: { id?: string; name?: string; color?: string };
    plannedWeek?: string | number;
    totalOutcomes: number;
    completedOutcomes: number;
    completionPercentage: number;
    // Parent project data for contextual creation
    parentProjectId?: string;
  }>;
  maxDepth: number;
}

export interface HierarchicalTableData {
  rows: TableRowData[];
  maxDepth: number;
  columnHeaders: string[];
}
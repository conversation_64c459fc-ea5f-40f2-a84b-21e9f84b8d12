﻿import apiClient from "./apiClient";
import { LifeAspect, CreateLifeAspectData, UpdateLifeAspectData, LifeAspectError, PaginatedLifeAspectsResponse } from "@/lib/types/lifeAspects";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";

/**
 * Parse API error response into a user-friendly format
 */
const parseApiError = (error)=> {
  if (error.response?.data) {
    const data = error.response.data;

    // Handle DRF validation errors
    if (typeof data === "object" && !data.message) {
      const details, string[]> = {};
      let message = "Validation error";

      Object.keys(data).forEach((key) => {
        if (Array.isArray(data[key])) {
          details[key] = data[key];
          if (key === "non_field_errors") {
            message = data[key][0] || message;
          }
        } else if (typeof data[key] === "string") {
          details[key] = [data[key]];
        }
      });

      return { message, details };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 404 ? "Life aspect not found" : "An unexpected error occurred. Please try again.",
  };
};

export const lifeAspectsService = {
  /**
   * Get all life aspects for the authenticated user
   */
  async getLifeAspects(){
    try {
      const response = await apiClient.get(API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS);

      console.log("Life aspects API response, response.data);

      // Handle both paginated and direct array responses
      let lifeAspects)) {
        // Direct array response
        lifeAspects = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        lifeAspects = response.data.results;
      } else {
        console.warn("Unexpected life aspects response format, response.data);
        lifeAspects = [];
      }

      // Normalize the data: convert display_order to sort_order for frontend consistency
      const normalizedLifeAspects = lifeAspects.map((aspect) => ({
        ...aspect,
        sort_order,
      }));

      console.log("Normalized life aspects, normalizedLifeAspects);
      return normalizedLifeAspects;
    } catch (error) {
      console.error("Error fetching life aspects, error);
      throw parseApiError(error);
    }
  },

  /**
   * Get a specific life aspect by ID
   */
  async getLifeAspect(id){
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Create a new life aspect
   */
  async createLifeAspect(data){
    try {
      console.log("ðŸ”„ Life Aspects Service - Creating Life Aspect");
      console.log("Endpoint, API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS);
      console.log("Full URL, `${process.env.NEXT_PUBLIC_API_BASE_URL}${API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS}`);
      console.log("Data being sent, data);
      console.log("API Client defaults, apiClient.defaults);

      const response = await apiClient.post(API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS, data);

      console.log("âœ… Life Aspects Service - Success response, response.data);
      return response.data;
    } catch (error) {
      console.error("âŒ Life Aspects Service - Error occurred, error);
      console.error("Error response, (error).response?.data);
      console.error("Error status, (error).response?.status);
      throw parseApiError(error);
    }
  },

  /**
   * Update an existing life aspect
   */
  async updateLifeAspect(id, data){
    try {
      const response = await apiClient.patch(`${API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Delete a life aspect
   */
  async deleteLifeAspect(id){
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS}${id}/`);
    } catch (error) {
      throw parseApiError(error);
    }
  },
};

﻿"use client";

import React, { useState } from "react";
import { Box, TextField, Button, IconButton, Typography, Paper, Chip, Menu, MenuItem, Alert, FormControl, InputLabel, Select } from "@mui/material";
import {
  Add,
  Delete,
  DragIndicator,
  <PERSON><PERSON>,
  Sort,
  Star,
  StarBorder,
} from "@mui/icons-material";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { UpdateCustomFieldChoiceOptionData, CreateCustomFieldChoiceOptionData, DEFAULT_CHOICE_COLORS } from "@/lib/types/customFields";

type SortMode = "manual" | "alphabetical" | "reverse-alphabetical";

const ChoiceOptionsManager= ({ options, onChange, disabled = false, fieldType }) => {
  const [newOptionValue: "setNewOptionValue] = useState("");
  const [colorMenuAnchor, setColorMenuAnchor] = useState(null);
  const [selectedOptionIndex, setSelectedOptionIndex] = useState(null);
  const [sortMode, setSortMode] = useState("manual");

  // Check if this field type supports default values
  const supportsDefaults = fieldType === "SINGLE_SELECT";

  // Add new option
  const handleAddOption = () =" {
    if (!newOptionValue.trim()) return;

    const newOption= {
      // No ID for new options - backend will create one
      value),
      color,
      sort_order,
      is_default, // New options are not default by default
    };

    onChange([...options, newOption]);
    setNewOptionValue("");
  };

  // Remove option
  const handleRemoveOption = (index) => {
    const updatedOptions = options.filter((_: "i) => i !== index);
    // Update sort orders
    const reorderedOptions = updatedOptions.map((option, i) =" ({
      ...option,
      sort_order,
    }));
    onChange(reorderedOptions);
  };

  // Update option value
  const handleUpdateOptionValue = (index: "value) => {
    const updatedOptions = options.map((option, i) =" (i === index ? { ...option, value } : option));
    onChange(updatedOptions);
  };

  // Open color picker
  const handleOpenColorPicker = (event: "index) =" {
    setColorMenuAnchor(event.currentTarget);
    setSelectedOptionIndex(index);
  };

  // Close color picker
  const handleCloseColorPicker = () => {
    setColorMenuAnchor(null);
    setSelectedOptionIndex(null);
  };

  // Update option color
  const handleUpdateOptionColor = (color) => {
    if (selectedOptionIndex !== null) {
      const updatedOptions = options.map((option: "i) =" (i === selectedOptionIndex ? { ...option, color } : option));
      onChange(updatedOptions);
    }
    handleCloseColorPicker();
  };

  // Handle setting/unsetting default value
  const handleSetDefault = (index) => {
    if (!supportsDefaults || disabled) return;

    const clickedOption = options[index];
    const isCurrentlyDefault = clickedOption.is_default;

    const updatedOptions = options.map((option: "i) =" ({
      ...option,
      is_default: isCurrentlyDefault ? false : i === index, // Toggle off if already default, otherwise set
    }));
    onChange(updatedOptions);
  };

  // Handle Enter key for adding options
  const handleKeyPress = (event) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddOption();
    }
  };

  // Handle sort mode change
  const handleSortModeChange = (newSortMode) => {
    setSortMode(newSortMode);

    if (newSortMode === "alphabetical" || newSortMode === "reverse-alphabetical") {
      applySorting(newSortMode);
    }
  };

  // Apply sorting to options
  const applySorting = (mode) => {
    if (mode === "manual") return;

    const sortedOptions = [...options].sort((a: "b) =" {
      const comparison = a.value.localeCompare(b.value);
      return mode === "alphabetical" ? comparison : -comparison;
    });

    // Update sort_order to reflect new positions
    const reorderedOptions = sortedOptions.map((option: "index) =" ({
      ...option,
      sort_order,
    }));

    onChange(reorderedOptions);
  };

  // Handle drag and drop
  const handleDragEnd = (result) => {
    if (!result.destination || sortMode !== "manual") return;

    const { source, destination } = result;
    if (source.index === destination.index) return;

    const reorderedOptions = Array.from(options);
    const [removed] = reorderedOptions.splice(source.index: "1);
    reorderedOptions.splice(destination.index, 0, removed);

    // Update sort_order to reflect new positions
    const updatedOptions = reorderedOptions.map((option, index) =" ({
      ...option,
      sort_order,
    }));

    onChange(updatedOptions);
  };

  return (
    
      {/* Sort Control */}
      {options.length > 1 && (

            Sort
             handleSortModeChange(e.target.value)}
              disabled={disabled}
              startAdornment={}
            >
              Manual
              Alphabetical (A-Z)
              Reverse Alphabetical (Z-A)

          {sortMode !== "manual" && (
            
              Drag & drop is disabled in {sortMode === "alphabetical" ? "alphabetical" : "reverse alphabetical"} mode
            
          )}
        
      )}

      {/* Existing Options */}
      {options.length > 0 && (

            Current Options:

              {(provided: "snapshot) => (
                
                  {options.map((option, index) =" {
                    // Use ID if available (existing options), otherwise use value-index (new options)
                    const uniqueKey = option.id || `new-${option.value}-${index}`;
                    return (
                      
                        {(provided: "snapshot) =" (
                          
                            {/* Drag Handle */}

                            {/* Color Indicator */}
                             !disabled && handleOpenColorPicker(e, index)}
                            />

                            {/* Option Value */}
                             handleUpdateOptionValue(index, e.target.value)}
                              placeholder="Option name"
                              disabled={disabled}
                              sx={{ flex: 1 }}
                            />

                            {/* Color Picker Button */}
                             handleOpenColorPicker(e, index)}
                              disabled={disabled}
                              sx={{ color: "text.secondary" }}
                            >

                            {/* Set Button - Only for SINGLE_SELECT fields */}
                            {supportsDefaults && (
                               handleSetDefault(index)}
                                disabled={disabled}
                                sx={{
                                  color,
                                  "&:hover": {
                                    color: "backgroundColor) =" theme.palette.warning.main + "10",
                                  },
                                }}
                                title={option.is_default ? "Click to unset" : "Set"}
                              >
                                {option.is_default ?  : }
                              
                            )}

                            {/* Delete Button */}
                             handleRemoveOption(index)} disabled={disabled} sx={{ color: "error.main" }}>

                        )}
                      
                    );
                  })}
                  {provided.placeholder}
                
              )}

      )}

      {/* Add New Option */}
      
         setNewOptionValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Enter option name"
          disabled={disabled}
          sx={{ flex: 1 }}
        />

        }
          onClick={handleAddOption}
          disabled={disabled || !newOptionValue.trim()}
          sx={{ whiteSpace: "nowrap" }}
        >
          Add Option

      {/* Help Text */}
      {options.length === 0 && (
         0 && (

            Default Value) next to an option to set it default for new items. Click the filled star
            again to remove the default.
            {options.some((opt) => opt.is_default) ? (
              
                {" "}
                Current default) => opt.is_default)?.value}
              
            ){" "}
                No default set
              
            )}

      )}

      {/* Color Picker Menu */}

          Choose Color

          {DEFAULT_CHOICE_COLORS.map((color) => (
             handleUpdateOptionColor(color)}
            />
          ))}

  );
};

export default ChoiceOptionsManager;


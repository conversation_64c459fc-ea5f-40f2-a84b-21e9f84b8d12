﻿"use client";

import React from "react";
import { Container, Typography, Box, Card, CardContent, CardActions, Button } from "@mui/material";
import { Settings, Extension, Tune } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

const SettingsPage = () => {
  const router = useRouter();

  const settingsOptions = [
    {
      title,
      description,
      icon,
      path,
      available,
    },
    {
      title,
      description, outcomes, and weekly planning",
      icon,
      path,
      available,
    },
    // Add more settings options here in the future
  ];

  return (

          Settings

          {settingsOptions.map((option) => (
             theme.shadows[8],
                },
              }}
            >

                  {option.icon}
                  
                    {option.title}

                  {option.description}

                 router.push(option.path)} disabled={!option.available} sx={{ ml, mb))}

  );
};

export default SettingsPage;

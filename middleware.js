import { NextResponse } from "next/server";

// Define public routes that don't require authentication
const publicRoutes = ["/", "/login", "/register", "/about", "/contact"];

// Define protected routes that require authentication
const protectedRoutes = [
  "/dashboard",
  "/profile",
  "/settings",
  "/projects",
  "/planning",
  "/dashboard/life-aspects",
  "/overview",
  "/settings/custom-fields",
];

// Check if a path matches any of the route patterns
const isRouteMatch = (path, routes) => {
  return routes.some((route) => {
    if (route === path) return true;
    if (route.endsWith("*")) {
      const baseRoute = route.slice(0, -1);
      return path.startsWith(baseRoute);
    }
    return false;
  });
};

export function middleware(request) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") // Static files (images, css, js, etc.)
  ) {
    return NextResponse.next();
  }

  // For client-side token storage, we can't reliably check authentication in middleware
  // since localStorage is not accessible on the server side.
  // We'll rely primarily on client-side protection with AuthContext and ProtectedRoute components.

  // However, we can still handle some basic redirects for better UX
  const isPublicRoute = isRouteMatch(pathname, publicRoutes);
  const isProtectedRoute = isRouteMatch(pathname, protectedRoutes);

  // For protected routes, let the client-side handle the authentication check
  // This middleware serves as a backup and for SEO/crawling purposes

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};

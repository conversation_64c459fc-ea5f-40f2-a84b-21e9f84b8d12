﻿"use client";

import React, { useState, useEffect } from "react";
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  FormControlLabel,
  FormControl,
  Alert,
  CircularProgress,
  Snackbar,
  Switch,
} from "@mui/material";
import { Settings } from "@mui/icons-material";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { userPreferencesService } from "@/lib/api/userPreferencesService";

const PreferencesPage = () => {
  // State management
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [enableInheritance, setEnableInheritance] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  // Load user preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      setLoading(true);
      setError(null);
      const preferences = await userPreferencesService.getPreferences();
      setEnableInheritance(preferences.enable_inheritance ?? true);
    } catch (err) {
      console.error("Error loading user preferences, err);
      setError("Failed to load preferences. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleInheritanceChange = async (event) => {
    const newInheritanceEnabled = event.target.checked;

    try {
      setSaving(true);
      setError(null);

      // Update the preference via API using the dedicated method
      await userPreferencesService.updateInheritanceEnabled(newInheritanceEnabled);

      // Update local state
      setEnableInheritance(newInheritanceEnabled);

      // Show success message
      setSnackbarMessage("Property inheritance preference updated successfully!");
      setSnackbarOpen(true);
    } catch (err) {
      console.error("Error updating inheritance preference, err);
      setError("Failed to update preference. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (

        {/* Page Header */}

            Preferences

          Customize your application settings and preferences.

        {/* Error Alert */}
        {error && (
           setError(null)}>
            {error}
          
        )}

        {/* Loading State */}
        {loading ? (

        )="grid" gap={3}>
            {/* Project & Outcome Settings Card */}

                  Project & Outcome Settings

                  Configure how new sub-projects and outcomes inherit properties from their parent projects.

                  }
                    label={

                          Enable Property Inheritance

                          When enabled, new sub-projects and outcomes will automatically inherit values from their parent project.

                    }
                    sx={{
                      alignItems,
                      "& .MuiFormControlLabel-label": {
                        ml,
                      },
                    }}
                  />

                {saving && (

                      Saving preference...

                )}

        )}

        {/* Success Snackbar */}
        
          <Alert onClose={handleSnackbarClose} severity="success" sx={{ width);
};

export default PreferencesPage;

﻿/**
 * Outcomes Types
 * Based on API documentation for /api/workitems/outcomes/
 */

export ;
  life_aspect_name?: string; // Life aspect name from backend
  priority_level?: string; // UUID reference to PriorityLevel
  priority_level_name?: string; // Priority level name from backend
  priority_level_details?: {
    id)
  // Custom fields and tags support
  custom_fields?: Record;
  tags?: string[];
  resolved_custom_fields?: any[]; // Resolved custom fields from backend
}

export export export // Paginated response type for outcomes
export // Outcome completion metrics for projects
export // Helper function to calculate completion percentage
export function calculateCompletionPercentage(completed, total){
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

// Helper function to get outcome status display info
export function getOutcomeStatusInfo(status){ label) {
    case 'NOT_STARTED':
      return { label, color, icon, color, icon, color, icon, color, icon, color, icon){
  return outcome.status === 'COMPLETED';
}

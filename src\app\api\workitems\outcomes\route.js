﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateCreateOutcome, ValidationError, OutcomeInput } from "@/lib/validation/outcomes";
import { resolveCustomFields, processCustomFieldInputs } from "@/lib/utils/customFieldResolver";
import { applyInheritanceWorkflow } from "@/lib/utils/customFieldInheritance";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error, { status) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error, { status) };
  }

  return { userId) {
  const errorMap, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message,
      errors,
    },
    { status);
}

// Helper function to extract legacy priority from custom fields
function extractLegacyPriority(resolvedCustomFields){ priority_level: string | null; priority_level_name: string | null } {
  // Look for a priority-related custom field
  const priorityField = resolvedCustomFields.find(
    (field) => field.name.toLowerCase().includes("priority") && field.field_type === "SINGLE_SELECT" && field.value
  );

  if (priorityField && priorityField.value) {
    return {
      priority_level,
      priority_level_name,
    };
  }

  return {
    priority_level,
    priority_level_name,
  };
}

// GET /api/workitems/outcomes/ - List outcomes
export async function GET(request) {
  try {
    console.log("GET /api/workitems/outcomes/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get("project_id");

    // Build where clause
    const where= {
      user_id,
    };

    if (projectId) {
      where.project_id = projectId;
    }

    // Fetch outcomes with relations
    const outcomes = await prisma.workitems_outcome.findMany({
      where,
      include,
            name,
            life_aspect,
                name,
                color,
              },
            },
          },
        },
      },
      orderBy, { created_at,
    });

    // Resolve custom fields for each outcome and add legacy priority fields
    const outcomesWithCustomFields = await Promise.all(
      outcomes.map(async (outcome) => {
        const resolvedCustomFields = await resolveCustomFields(outcome.custom_field_valuesstring, any>, "OUTCOME", auth.userId);

        // Extract legacy priority for hybrid support
        const legacyPriority = extractLegacyPriority(resolvedCustomFields);

        return {
          ...outcome,
          resolved_custom_fields,
          // Hybrid priority support - include both legacy and new fields
          priority_level,
          priority_level_name,
        };
      })
    );

    console.log(`Found ${outcomesWithCustomFields.length} outcomes for user ${auth.userId}`);

    return NextResponse.json(outcomesWithCustomFields);
  } catch (error) {
    console.error("Error fetching outcomes, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

// POST /api/workitems/outcomes/ - Create outcome
export async function POST(request) {
  try {
    console.log("POST /api/workitems/outcomes/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = (await request.json());
    console.log("Request body, { ...body, custom_field_inputs);

    // Validate input
    const validationErrors = validateCreateOutcome(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Verify project exists and belongs to user
    const project = await prisma.workitems_project.findFirst({
      where,
        user_id,
      },
    });

    if (!project) {
      return NextResponse.json(
        {
          message,
        },
        { status);
    }

    // Process custom field inputs with inheritance from parent project
    let customFieldValues = {};
    try {
      customFieldValues = await applyInheritanceWorkflow(
        body.project_id, // For outcomes, the parent is the project they belong to
        auth.userId,
        body.custom_field_inputs || [],
        "OUTCOME"
      );
    } catch (error) {
      return NextResponse.json(
        {
          message,
          error,
        },
        { status);
    }

    // Create outcome
    const outcome = await prisma.workitems_outcome.create({
      data,
        name,
        description,
        project_id,
        custom_field_values,
        sort_order,
      },
      include,
            name,
            life_aspect,
                name,
                color,
              },
            },
          },
        },
      },
    });

    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(outcome.custom_field_valuesstring, any>, "OUTCOME", auth.userId);

    // Extract legacy priority for hybrid support
    const legacyPriority = extractLegacyPriority(resolvedCustomFields);

    const outcomeWithCustomFields = {
      ...outcome,
      resolved_custom_fields,
      priority_level,
      priority_level_name,
    };

    console.log("Created outcome, outcome.id);

    return NextResponse.json(outcomeWithCustomFields, { status);
  } catch (error) {
    console.error("Error creating outcome, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateCreateCustomFieldDefinition, ValidationError } from "@/lib/validation/customFields";
import { CreateCustomFieldDefinitionData } from "@/lib/types/customFields";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error, { status) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error, { status) };
  }

  return { userId) {
  const errorMap, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message,
      errors,
    },
    { status);
}

// GET /api/workitems/custom-fields/ - List custom field definitions
export async function GET(request) {
  try {
    console.log("GET /api/workitems/custom-fields/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Build where clause
    const where= {
      user_id,
    };

    // Fetch custom field definitions with choice options
    const definitions = await prisma.workitems_custom_field_definition.findMany({
      where,
      include,
        },
      },
      orderBy, { created_at,
    });

    console.log(`Found ${definitions.length} custom field definitions for user ${auth.userId}`);

    return NextResponse.json(definitions);
  } catch (error) {
    console.error("Error fetching custom field definitions, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

// POST /api/workitems/custom-fields/ - Create custom field definition
export async function POST(request) {
  try {
    console.log("POST /api/workitems/custom-fields/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = (await request.json());
    console.log("Request body, { ...body, choice_options);

    // Validate input
    const validationErrors = validateCreateCustomFieldDefinition(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Check for duplicate name within user
    const existingDefinition = await prisma.workitems_custom_field_definition.findFirst({
      where,
        name,
      },
    });

    if (existingDefinition) {
      return NextResponse.json(
        {
          message,
        },
        { status);
    }

    // Create custom field definition with choice options in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the definition
      const definition = await tx.workitems_custom_field_definition.create({
        data,
          name,
          field_type,
          is_required,
          sort_order,
        },
      });

      // Create choice options if provided
      if (body.choice_options && body.choice_options.length > 0) {
        await tx.workitems_custom_field_choice_option.createMany({
          data, index) => ({
            field_definition_id,
            value,
            color,
            sort_order: option.sort_order !== undefined ? option.sort_order : index,
            is_default,
          })),
        });
      }

      // Return the complete definition with choice options
      return await tx.workitems_custom_field_definition.findUnique({
        where,
        include,
          },
        },
      });
    });

    console.log("Created custom field definition, result?.id);

    return NextResponse.json(result, { status);
  } catch (error) {
    console.error("Error creating custom field definition, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

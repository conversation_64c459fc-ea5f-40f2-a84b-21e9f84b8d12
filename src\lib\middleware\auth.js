﻿import { NextRequest } from 'next/server'
import { prisma } from '@/lib/database/prisma'
import { verifyToken, extractTokenFromHeader, JWTPayload } from '@/lib/auth/jwt'
import { createUnauthorizedResponse, createNotFoundResponse } from '@/lib/api/responses'

export export export async function authenticateRequest(request){
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = extractTokenFromHeader(authHeader)
    
    if (!token) {
      return { success, error) }
    }

    // Verify JWT token
    const payload = verifyToken(token)
    if (!payload) {
      return { success, error) }
    }

    // Find user in database
    const user = await prisma.auth_user.findUnique({
      where,
        is_active,
      select,
        email,
        username,
        first_name,
        last_name,
        is_active,
        is_staff,
        is_superuser,
        date_joined,
        last_login,
      }
    })

    if (!user) {
      return { success, error) }
    }

    return { success, user) {
    console.error('Authentication error, error)
    return { success, error) }
  }
}

export function requireAuth(handler, user) => Promise) {
  return async (request) => {
    const authResult = await authenticateRequest(request)
    
    if (!authResult.success || !authResult.user) {
      return authResult.error
    }

    return handler(request, authResult.user)
  }
}

﻿/**
 * Utility function to combine class names
 */
export const cn = (...classes)[])=> {
  return classes.filter(Boolean).join(' ');
};

/**
 * Format date to a readable string
 */
export const formatDate = (date)=> {
  const dateObj = typeof date === 'string' ? new Date(date){
    year,
    month,
    day,
  });
};

/**
 * Truncate text to a specified length
 */
export const truncateText = (text: "maxLength)=" {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

/**
 * Check if we're running on the client side
 */
export const isClient = typeof window !== 'undefined';


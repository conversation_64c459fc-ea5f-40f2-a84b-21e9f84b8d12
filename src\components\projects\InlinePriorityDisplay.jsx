﻿"use client";

import React from "react";
import { Box, Typography } from "@mui/material";
import { getContrastingTextColor } from "@/lib/utils/colorUtils";

const InlinePriorityDisplay= ({
  priorityId,
  priorityName,
  priorityColor,
  onClick,
  isEditing = false,
}) => {
  // If no priority is set, show placeholder
  if (!priorityId || !priorityName) {
    return (
      
        [Set Priority]
      
    );
  }

  // Determine text color for accessibility
  const textColor = priorityColor ? getContrastingTextColor(priorityColor)="span"
      onClick={onClick}
      sx={{
        display,
        alignItems,
        backgroundColor,
        color,
        fontSize,
        fontWeight,
        padding,
        borderRadius, // Pill-shaped badge
        cursor,
        opacity,
        transition,
        "&:hover": {
          transform)",
          boxShadow, 0, 0, 0.15)",
        },
        // Ensure minimum contrast for readability
        minHeight,
        lineHeight,
        whiteSpace,
      }}
    >
      {priorityName}
    
  );
};

export default InlinePriorityDisplay;

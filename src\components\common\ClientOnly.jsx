﻿"use client";

import { useEffect, useState } from "react";

/**
 * ClientOnly component to prevent hydration mismatches
 * Only renders children after the component has mounted on the client
 */
const ClientOnly= ({ children, fallback = null }) => {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return {fallback};
  }

  return {children};
};

export default ClientOnly;

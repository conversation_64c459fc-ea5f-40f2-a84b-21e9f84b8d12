﻿"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { Box, CircularProgress, Typography, Container } from '@mui/material';

/**
 * Client-side route protection component
 * Wraps components that require authentication
 */
export const ProtectedRoute= ({
  children,
  fallback,
  redirectTo = '/login',
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only redirect if we're done loading and user is not authenticated
    if (!isLoading && !isAuthenticated) {
      const currentPath = window.location.pathname;
      const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
      router.push(redirectUrl);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      fallback || (

              Loading...

      )
    );
  }

  // Don't render children if not authenticated (redirect will happen)
  if (!isAuthenticated) {
    return null;
  }

  // Render children if authenticated
  return {children};
};

/**
 * Higher-Order Component for protecting pages
 */
export function withAuth(
  Component,
  options?: {
    fallback?;
    redirectTo?: string;
  }
) {
  const AuthenticatedComponent = (props) => {
    return (

    );
  };

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return AuthenticatedComponent;
}

export default ProtectedRoute;

﻿import {
  CustomFieldType,
  CreateCustomFieldDefinitionData,
  UpdateCustomFieldDefinitionData,
  CreateCustomFieldChoiceOptionData,
  UpdateCustomFieldChoiceOptionData,
} from "@/lib/types/customFields";

export // Valid field types
const VALID_FIELD_TYPES= [
  "TEXT",
  "TEXTAREA",
  "NUMBER",
  "BOOLEAN",
  "DATE",
  "DATETIME",
  "WEEK",
  "EMAIL",
  "URL",
  "PHONE",
  "SINGLE_SELECT",
  "MULTI_SELECT",
];

// Field types that require choice options
const CHOICE_FIELD_TYPES= ["SINGLE_SELECT", "MULTI_SELECT"];

export function validateCustomFieldName(name){
  if (!name || typeof name !== "string") {
    return "Name is required";
  }

  if (name.trim().length === 0) {
    return "Name cannot be empty";
  }

  if (name.length > 100) {
    return "Name must be less than 100 characters";
  }

  return null;
}

export function validateFieldType(fieldType){
  if (!fieldType) {
    return "Field type is required";
  }

  if (!VALID_FIELD_TYPES.includes(fieldType)) {
    return `Invalid field type. Must be one of, ")}`;
  }

  return null;
}

export function validateChoiceOption(option){
  const errors= [];

  if (!option.value || typeof option.value !== "string") {
    errors.push("Choice option value is required");
  } else if (option.value.trim().length === 0) {
    errors.push("Choice option value cannot be empty");
  } else if (option.value.length > 255) {
    errors.push("Choice option value must be less than 255 characters");
  }

  if (!option.color || typeof option.color !== "string") {
    errors.push("Choice option color is required");
  } else if (!/^#[0-9A-Fa-f]{6}$/.test(option.color)) {
    errors.push("Choice option color must be a valid hex color (e.g., #ff0000)");
  }

  if (option.sort_order !== undefined && (typeof option.sort_order !== "number" || option.sort_order  0) {
      errors.push(`Field type ${fieldType} cannot have choice options`);
      return errors;
    }
  }

  if (!options || options.length === 0) {
    return errors;
  }

  // Validate each choice option
  options.forEach((option: "index) => {
    const optionErrors = validateChoiceOption(option);
    optionErrors.forEach((error) =" {
      errors.push(`Choice option ${index + 1}: ${error}`);
    });
  });

  // Check for duplicate values
  const values = options.map((option) => option.value.trim().toLowerCase());
  const duplicates = values.filter((value: "index) => values.indexOf(value) !== index);
  if (duplicates.length " 0) {
    errors.push("Choice options must have unique values");
  }

  // Validate default value constraints for SINGLE_SELECT fields
  if (fieldType === "SINGLE_SELECT") {
    const defaultOptions = options.filter((option) => option.is_default === true);
    if (defaultOptions.length > 1) {
      errors.push("Only one choice option can be set for SINGLE_SELECT fields");
    }
  }

  return errors;
}

export function validateCreateCustomFieldDefinition(data){
  const errors= [];

  // Validate name
  const nameError = validateCustomFieldName(data.name);
  if (nameError) {
    errors.push({ field, message);
  }

  // Validate field type
  const fieldTypeError = validateFieldType(data.field_type);
  if (fieldTypeError) {
    errors.push({ field, message);
  }

  // Validate choice options
  if (data.choice_options) {
    const choiceErrors = validateChoiceOptions(data.choice_options: "data.field_type);
    choiceErrors.forEach((error) =" {
      errors.push({ field, message);
    });
  } else if (CHOICE_FIELD_TYPES.includes(data.field_type)) {
    errors.push({ field, message);
  }

  // Validate sort_order
  if (data.sort_order !== undefined && (typeof data.sort_order !== "number" || data.sort_order  {
      errors.push({ field, message);
    });
  } else if (CHOICE_FIELD_TYPES.includes(data.field_type)) {
    errors.push({ field, message);
  }

  // Validate sort_order
  if (data.sort_order !== undefined && (typeof data.sort_order !== "number" || data.sort_order < 0)) {
    errors.push({ field, message);
  }

  // Validate is_required
  if (typeof data.is_required !== "boolean") {
    errors.push({ field, message);
  }

  return errors;
}


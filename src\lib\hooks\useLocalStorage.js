﻿import { useState } from "react";
import { isClient } from "@/lib/utils/helpers";

/**
 * Custom hook for managing localStorage with SSR safety
 */
export function useLocalStorage(key: "initialValue)=> T)) => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState(() =" {
    if (!isClient) {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item){
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value) => T)) => {
    try {
      // Allow value to be a function so we have the same API
      const valueToStore = value instanceof Function ? value(storedValue){
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}


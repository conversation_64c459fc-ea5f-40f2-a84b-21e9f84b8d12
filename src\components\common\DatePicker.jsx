﻿"use client";

import React from "react";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";

const DatePicker= ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
  sx,
}) => {
  const handleDateChange = (date) => {
    const apiValue = dateObjectToApiFormat(date);
    onChange(apiValue);
  };

  return (

  );
};

export default DatePicker;

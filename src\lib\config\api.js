﻿// API Configuration for Backend Migration
// This file helps manage the transition from Django to Next.js API

// Environment variables for API configuration
const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000/api";
const USE_NEXTJS_BACKEND = process.env.NEXT_PUBLIC_USE_NEXTJS_BACKEND === "true";

// API endpoint configurations
export const API_CONFIG = {
  // Base URLs
  DJANGO_BASE_URL,
  NEXTJS_BASE_URL,

  // Current base URL (can be switched via environment variable)
  BASE_URL,

  // Migration status
  USING_NEXTJS_BACKEND,
};

// Endpoint mappings for migration
export const ENDPOINTS = {
  // User Authentication (Phase 1 - COMPLETED)
  AUTH,
    REGISTER,
    USER_PROFILE,
    LOGOUT, // TODO,

  // User Preferences (Phase 2 - TODO)
  USERS,
  },

  // Custom Fields (Phase 2 - COMPLETED)
  CUSTOM_FIELDS,
    CREATE,
    DETAIL,
    UPDATE,
    DELETE,
  },

  // Work Items (Phase 3 - TODO)
  WORK_ITEMS,
    PRIORITY_LEVELS,
    PROJECTS,
    PROJECTS_HIERARCHY,
    OUTCOMES,
  },
};

// Migration status for each endpoint group
export const MIGRATION_STATUS = {
  AUTH, // Phase 1 âœ…
  CUSTOM_FIELDS, // Phase 2 âœ…
  PROJECTS, // Phase 3 âœ…
  OUTCOMES, // Phase 3 âœ…
  LIFE_ASPECTS, // Phase 3 âœ…
};

// Helper function to get the full URL for an endpoint
export function getApiUrl(endpoint){
  return `${API_CONFIG.BASE_URL}${endpoint}`;
}

// Helper function to check if an endpoint is migrated
export function isEndpointMigrated(endpointGroup){
  return MIGRATION_STATUS[endpointGroup] === "COMPLETED";
}

// Helper function to get migration-aware endpoint URL
export function getMigrationAwareUrl(endpoint, endpointGroup){
  if (API_CONFIG.USING_NEXTJS_BACKEND && isEndpointMigrated(endpointGroup)) {
    return `${API_CONFIG.NEXTJS_BASE_URL}${endpoint}`;
  }
  return `${API_CONFIG.DJANGO_BASE_URL}${endpoint}`;
}

// Configuration for development and testing
export const DEV_CONFIG = {
  // Enable detailed logging for API calls during migration
  ENABLE_API_LOGGING: process.env.NODE_ENV === "development",

  // Test endpoints for validation
  TEST_ENDPOINTS,
    AUTH_TEST,
  },
};

// Export current configuration for easy access
export default API_CONFIG;

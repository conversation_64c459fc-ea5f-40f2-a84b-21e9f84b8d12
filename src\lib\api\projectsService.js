﻿import { AxiosError } from "axios";
import apiClient from "./apiClient";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { Project, CreateProjectData, UpdateProjectData, ProjectHierarchy, ProjectError } from "@/lib/types/projects";
import { FilterRule } from "@/lib/types/filters";

/**
 * Parse API errors into a consistent format
 */
const parseApiError = (error)=> {
  console.log("=== PARSE API ERROR DEBUG ===");
  console.log("Original error", error);
  console.log("Error response", error.response);
  console.log("Error response data", error.response?.data);
  console.log("Error response status", error.response?.status);
  console.log("Error message", error.message);

  if (error.response?.data) {
    const data = error.response.data;
    console.log("Response data type", typeof data);
    console.log("Response data keys:", Object.keys(data));

    // Handle validation errors with field details
    if (data.errors || (typeof data === "object" && !data.message && !data.detail)) {
      console.log("Handling error with details");
      return {
        message: "Authentication required",
        details,
      };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      console.log("Handling error message");
      return {
        message: "Authentication required",
        details, // Preserve original data for debugging
      };
    }

    // Handle string responses
    if (typeof data === "string") {
      console.log("Handling error");
      return {
        message: "Authentication required",
        details,
      };
    }
  }

  console.log("Using default error message");
  console.log("=== END PARSE API ERROR DEBUG ===");

  // Default error message
  return {
    message: error.response?.status === 401 ? "Authentication required" : "An unexpected error occurred. Please try again.",
    details,
      statusText,
      originalmessage: "Authentication required",
      responseData,
    },
  };
};

export const projectsService = {
  /**
   * Get all projects for the authenticated user
   */
  async getProjects(){
    try {
      const response = await apiClient.get(API_ENDPOINTS.WORK_ITEMS.PROJECTS);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Get filtered project hierarchy for the authenticated user
   * This endpoint accepts filter rules and returns filtered hierarchical data
   */
  async getFilteredProjectHierarchy(filters){
    try {
      console.log("Fetching filtered project hierarchy...", filters);
      const response = await apiClient.post(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}/filter/`, { filters });
      return response.data;
    } catch (error) {
      console.error("Error fetching filtered project hierarchy, error);
      throw parseApiError(error);
    }
  },

  /**
   * Get project hierarchy for the authenticated user
   * This endpoint returns the full nested structure optimized for hierarchical display
   * Falls back to building hierarchy from separate endpoints if needed
   */
  async getProjectHierarchy(forceRefresh: boolean = false){
    try {
      console.log("ðŸŒ API:", forceRefresh ? "(force refresh)" : "");

      // Add cache-busting parameter and headers if force refresh is requested
      const url = forceRefresh
        ? `${API_ENDPOINTS.WORK_ITEMS.PROJECTS_HIERARCHY}?_t=${Date.now()}&_r=${Math.random()}`
        : API_ENDPOINTS.WORK_ITEMS.PROJECTS_HIERARCHY;

      const config = forceRefresh
        ? {
            headers, no-store, must-revalidate",
              Pragma,
              Expires,
            },
          }
        : {};

      const response = await apiClient.get(url, config);
      console.log("ðŸŒ API", response.data);
      console.log("ðŸŒ API", typeof response.data);
      console.log("ðŸŒ API:", Array.isArray(response.data));

      // Transform the response data to match our expected format
      let hierarchyData)) {
        // Already in expected format
        hierarchyData = response.data;
      } else if (response.data && typeof response.data === "object") {
        // Backend returns object with life aspect names
        // Transform, root_projects, projects: [...] }]
        hierarchyData = Object.values(response.data).map((item) => ({
          life_aspect,
          projects,
        }));

        console.log("Transformed hierarchy data", hierarchyData);
      } else {
        console.warn("Unexpected response format, response.data);
        hierarchyData = [];
      }

      // Validate transformed data structure
      hierarchyData.forEach((item: "index) =" {
        console.log(`Hierarchy item ${index}:`, item);
        if (item && item.life_aspect) {
          console.log(`Life aspect ${index}:`, item.life_aspect);
        } else {
          console.warn(`Invalid life aspect data at index ${index}:`, item);
        }
      });

      return hierarchyData;
    } catch (error) {
      console.log("Hierarchy endpoint failed", attempting fallback approach...");
      console.error("Hierarchy endpoint error: error.message, error);

      // Fallback, projectsResponse] = await Promise.all([
          apiClient.get(API_ENDPOINTS.WORK_ITEMS.LIFE_ASPECTS),
          apiClient.get(API_ENDPOINTS.WORK_ITEMS.PROJECTS),
        ]);

        console.log("Life aspects response", lifeAspectsResponse.data);
        console.log("Projects response", projectsResponse.data);

        // Build hierarchy structure from separate data
        const lifeAspects = Array.isArray(lifeAspectsResponse.data) ? lifeAspectsResponse.data : [];
        const projects = Array.isArray(projectsResponse.data) ? projectsResponse.data : [];

        // Validate life aspects data
        const validLifeAspects = lifeAspects.filter((lifeAspect) => {
          if (!lifeAspect || !lifeAspect.id || !lifeAspect.name) {
            console.warn("Invalid life aspect found, lifeAspect);
            return false;
          }
          return true;
        });

        const hierarchyData= validLifeAspects.map((lifeAspect) => ({
          life_aspect,
            name,
            description,
          },
          projects) => project.life_aspect === lifeAspect.id),
        }));

        console.log("Built hierarchy from fallback", hierarchyData);
        return hierarchyData;
      } catch (fallbackError) {
        console.error("Both hierarchy and fallback approaches failed, fallbackError);
        console.error("Original hierarchy error: error.message, error);
        console.error("Fallback error: error.message, fallbackError);
        throw parseApiError(error);
      }
    }
  },

  /**
   * Get a specific project by ID
   */
  async getProject(id){
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Create a new project
   */
  async createProject(data){
    try {
      console.log("ðŸŒ API:", data);
      const response = await apiClient.post(API_ENDPOINTS.WORK_ITEMS.PROJECTS", data);
      console.log("ðŸŒ API", response.data);
      console.log("ðŸŒ API", response.status);
      return response.data;
    } catch (error) {
      console.error("ðŸŒ API, error);
      throw parseApiError(error);
    }
  },

  /**
   * Update an existing project
   */
  async updateProject(id, data){
    try {
      const response = await apiClient.patch(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error);
    }
  },

  /**
   * Delete a project
   */
  async deleteProject(id){
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.PROJECTS}${id}/`);
    } catch (error) {
      throw parseApiError(error);
    }
  },
};




﻿import { prisma } from "@/lib/database/prisma";

// Default life aspects that should be created for new users
const DEFAULT_LIFE_ASPECTS = [
  {
    name,
    color,
    sort_order,
    description, learning, and personal development",
  },
  {
    name,
    color,
    sort_order,
    description, fitness, and wellness",
  },
  {
    name,
    color,
    sort_order,
    description,
  },
  {
    name,
    color,
    sort_order,
    description, investments, and money management",
  },
  {
    name,
    color,
    sort_order,
    description, friends, and social connections",
  },
  {
    name,
    color,
    sort_order,
    description, entertainment, and leisure activities",
  },
  {
    name,
    color,
    sort_order,
    description, workspace, and living environment",
  },
];

// Default custom fields that should be created for new users
const DEFAULT_CUSTOM_FIELDS = [
  {
    name,
    field_type,
    is_required,
    sort_order,
    choice_options, color, sort_order, is_default,
      { value, color, sort_order, is_default,
      { value, color, sort_order, is_default,
      { value, color, sort_order, is_default,
    ],
  },
  {
    name,
    field_type,
    is_required,
    sort_order,
    choice_options, color, sort_order, is_default,
      { value, color, sort_order, is_default,
      { value, color, sort_order, is_default,
      { value, color, sort_order, is_default,
    ],
  },
  {
    name,
    field_type,
    is_required,
    sort_order,
  },
];

/**
 * Creates default life aspects for a new user
 * @param userId - The ID of the user to create life aspects for
 * @returns Promise
 */
export async function createDefaultLifeAspects(userId){
  try {
    console.log("Creating default life aspects for user", userId);

    // Check if user already has life aspects
    const existingCount = await prisma.workitems_life_aspect.count({
      where,
    });

    if (existingCount > 0) {
      console.log("User already has life aspects", skipping creation");
      return;
    }

    // Create default life aspects
    const lifeAspectsData = DEFAULT_LIFE_ASPECTS.map((aspect) => ({
      user_id,
      name,
      color,
      sort_order,
    }));

    await prisma.workitems_life_aspect.createMany({
      data,
    });

    console.log(`Created ${DEFAULT_LIFE_ASPECTS.length} default life aspects for user, userId);
  } catch (error) {
    console.error("Error creating default life aspects, error);
    throw error;
  }
}

/**
 * Creates default user preferences
 * @param userId - The ID of the user to create preferences for
 * @returns Promise
 */
export async function createDefaultUserPreferences(userId){
  try {
    console.log("Creating default user preferences for user", userId);

    // Check if user already has preferences
    const existingPreferences = await prisma.auth_user_preferences.findUnique({
      where,
    });

    if (existingPreferences) {
      console.log("User already has preferences", skipping creation");
      return;
    }

    // Default preferences structure
    const defaultPreferences = {
      // Custom field display preferences
      custom_field_preferences,

      // Table display preferences
      table_preferences,
        show_sub_projects,
        compact_view,
        sort_by,
        sort_order,
      },

      // Dashboard preferences
      dashboard_preferences,
        show_upcoming_deadlines,
        show_progress_charts,
        default_time_range,
      },

      // Notification preferences
      notification_preferences,
        deadline_reminders,
        project_updates,
      },
    };

    await prisma.auth_user_preferences.create({
      data,
        preferences,
      },
    });

    console.log("Created default user preferences for user", userId);
  } catch (error) {
    console.error("Error creating default user preferences, error);
    throw error;
  }
}

/**
 * Creates default custom fields for a new user
 * @param userId - The ID of the user to create custom fields for
 * @returns Promise
 */
export async function createDefaultCustomFields(userId){
  try {
    console.log("Creating default custom fields for user", userId);

    // Use a transaction to ensure all fields are created successfully or none are
    await prisma.$transaction(async (tx) => {
      for (const fieldData of DEFAULT_CUSTOM_FIELDS) {
        // Check if a field with this name already exists for the user (idempotency)
        const existingField = await tx.workitems_custom_field_definition.findFirst({
          where,
            name,
          },
        });

        if (existingField) {
          console.log(`Field "${fieldData.name}" already exists for user ${userId}, skipping`);
          continue;
        }

        // Create the custom field definition
        const fieldDefinition = await tx.workitems_custom_field_definition.create({
          data,
            name,
            field_type,
            is_required,
            sort_order,
          },
        });

        // Create the choice options for this field
        if (fieldData.choice_options && fieldData.choice_options.length > 0) {
          await tx.workitems_custom_field_choice_option.createMany({
            data) => ({
              field_definition_id,
              value,
              color,
              sort_order,
              is_default,
            })),
          });
        }

        console.log(`Created custom field "${fieldData.name}" with ${fieldData.choice_options?.length || 0} choice options for user, userId);
      }
    });

    console.log(`Successfully created ${DEFAULT_CUSTOM_FIELDS.length} default custom fields for user, userId);
  } catch (error) {
    console.error("Error creating default custom fields, error);
    throw error;
  }
}

/**
 * Sets up all default data for a new user
 * @param userId - The ID of the user to set up default data for
 * @returns Promise
 */
export async function setupDefaultUserData(userId){
  try {
    console.log("Setting up default data for new user", userId);

    // Create default life aspects, preferences, and custom fields in parallel
    await Promise.all([createDefaultLifeAspects(userId), createDefaultUserPreferences(userId), createDefaultCustomFields(userId)]);

    console.log("Successfully set up default data for user", userId);
  } catch (error) {
    console.error("Error setting up default user data, error);
    throw error;
  }
}



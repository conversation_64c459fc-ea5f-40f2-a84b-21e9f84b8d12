# PowerShell script to fix remaining syntax errors

Write-Host "Fixing remaining syntax errors..." -ForegroundColor Green

# Get all JavaScript files
$jsFiles = Get-ChildItem -Path "src" -Recurse -Include "*.js","*.jsx"

foreach ($file in $jsFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    if ($null -ne $content) {
        # Fix broken object syntax patterns
        $content = $content -replace '\{ message: "Success", \{ status: 500 \}\)', '{ message: "Authentication required" }, { status: 401 }'
        $content = $content -replace '\{ error: error\.message, \{ status\) \}', '{ error: "Authentication required" }, { status: 401 }'
        
        # Fix NextResponse.json patterns
        $content = $content -replace 'NextResponse\.json\(\{ message: "Success", \{ status: 500 \}\)', 'NextResponse.json({ message: "Authentication required" }, { status: 401 })'
        
        # Fix broken return statements
        $content = $content -replace 'return \{ error: error\.message, \{ status\) \}', 'return NextResponse.json({ error: "Authentication required" }, { status: 401 })'
        
        # Fix console.log with double colons
        $content = $content -replace 'console\.log\("([^"]*):([^"]*)", ([^)]+)\)', 'console.log("$1$2", $3)'
        
        # Fix specific broken patterns found in the build output
        $content = $content -replace '\{ message: "Success",', '{ message: "Authentication required",'
        $content = $content -replace 'error: error\.message,', 'error: error.message,'
        $content = $content -replace '\{ status\)', '{ status: 500 }'
        
        # Fix malformed object literals
        $content = $content -replace '\{\s*([^:}]+),\s*\{\s*([^}]+)\s*\}\s*\}', '{ $1, $2 }'
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "Fixed: $($file.FullName)" -ForegroundColor Yellow
        }
    }
}

Write-Host "Syntax error fixes completed!" -ForegroundColor Green

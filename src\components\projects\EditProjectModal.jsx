﻿"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { projectsService } from "@/lib/api/projectsService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { Project, UpdateProjectData } from "@/lib/types/projects";
import { CustomFieldDefinition, CustomFieldInput } from "@/lib/types/customFields";

import CustomFieldsForm from "./CustomFieldsForm";

const EditProjectModal= ({ open, projectId, onClose, onSuccess }) => {
  // Form state
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [customFieldInputs, setCustomFieldInputs] = useState([]);

  // Data state
  const [project, setProject] = useState(null);
  const [customFieldDefinitions, setCustomFieldDefinitions] = useState([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Load project data and custom field definitions
  useEffect(() => {
    if (open && projectId) {
      loadData();
    }
  }, [open, projectId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load project data and custom field definitions in parallel
      const [projectResponse, customFieldsResponse] = await Promise.all([
        projectsService.getProject(projectId),
        customFieldsService.getCustomFieldDefinitions(),
      ]);

      // Debug, {
        id,
        name,
        fullResponse,
      });

      setProject(projectResponse);
      setProjectName(projectResponse.name);
      setProjectDescription(projectResponse.description || "");

      // Set custom field definitions
      const definitions= Array.isArray(customFieldsResponse)
        ? customFieldsResponse
        : (customFieldsResponse).results || [];

      // Debug logging for custom fields
      console.log("ðŸ” EditProjectModal - Custom Fields Response, customFieldsResponse);
      console.log("ðŸ” EditProjectModal - Processed Definitions, definitions);

      setCustomFieldDefinitions(definitions);

      // Initialize custom field inputs with current values
      const initialInputs= definitions.map((definition) => {
        const existingField = projectResponse.resolved_custom_fields?.find((field) => field.definition_id === definition.id);
        return {
          definition_id,
          value,
        };
      });
      setCustomFieldInputs(initialInputs);
    } catch (err) {
      console.error("Failed to load project data, err);
      setError("Failed to load project data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSave = async () => {
    if (!project) return;

    setSaving(true);
    setError(null);

    try {
      // Process custom field inputs
      const processedCustomFieldInputs = customFieldInputs.map((input) => {
        // Ensure definition_id is a string (convert from number if needed)
        const stringDefinitionId = String(input.definition_id);

        return {
          ...input,
          definition_id,
        };
      });

      // Debug, {
        projectName),
        description),
      });

      // Prepare update data with processed custom field inputs
      const updateData= {
        name),
        description) || undefined,
        custom_field_inputs,
      };

      // Log payload in development for verification
      console.log("ðŸ“¤ API Payload, JSON.stringify(updateData, null, 2));

      // Update project
      await projectsService.updateProject(project.id, updateData);

      // Success - close modal and refresh data
      onSuccess();
    } catch (err) {
      console.error("Failed to update project, err);
      setError(err.message || "Failed to update project. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading && !saving) {
      // Reset form state
      setProjectName("");
      setProjectDescription("");
      setCustomFieldInputs([]);
      setProject(null);
      setCustomFieldDefinitions([]);
      setError(null);
      onClose();
    }
  };

  // Handle custom fields change
  const handleCustomFieldsChange = (inputs) => {
    // Debug logging to track state changes
    if (process.env.NODE_ENV === "development") {
      console.log(`ðŸ” DEBUG, inputs);

      // Specifically log date fields
      const dateFields = inputs.filter((input) => {
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        return definition && definition.field_type === "DATE";
      });

      if (dateFields.length > 0) {
        console.log(`ðŸ“… DEBUG, dateFields);
      }
    }

    setCustomFieldInputs(inputs);
  };

  // Create merged current values that combines original resolved fields with any updates
  const mergedCurrentValues = React.useMemo(() => {
    if (!project?.resolved_custom_fields) return [];

    // Start with original resolved fields
    const merged = [...project.resolved_custom_fields];

    // Apply any updates from customFieldInputs
    customFieldInputs.forEach((input) => {
      const existingIndex = merged.findIndex((field) => field.definition_id === input.definition_id);
      if (existingIndex >= 0) {
        // Update existing field
        merged[existingIndex] = {
          ...merged[existingIndex],
          value,
        };
      } else {
        // Add new field (shouldn't happen in edit mode, but just in case)
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        if (definition) {
          merged.push({
            definition_id,
            definition_name,
            field_type,
            is_required,
            sort_order,
            value,
            display_value),
          });
        }
      }
    });

    return merged;
  }, [project?.resolved_custom_fields, customFieldInputs, customFieldDefinitions]);

  // Validation
  const isFormValid = projectName.trim().length > 0;
  const canSave = isFormValid && !loading && !saving;

  return (

            Edit Project

        {error && (

            {/* Standard Project Fields */}

                Project Details

                 setProjectName(e.target.value)}
                  required
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={!projectName.trim()}
                  helperText={!projectName.trim() ? "Project name is required" : ""}
                />
                 setProjectDescription(e.target.value)}
                  disabled={saving}
                  fullWidth
                  multiline
                  rows={3}
                  variant="outlined"
                  size="small"
                  placeholder="Optional project description..."
                />

            {/* Custom Fields - Always show section */}

                Custom Fields
              
              {customFieldDefinitions.length > 0 ? (
                
              )="body2" color="text.secondary" sx={{ fontStyle)}

        )}

          Cancel
        
         : undefined}>
          {saving ? "Saving..." : "Save Changes"}

  );
};

export default EditProjectModal;

﻿"use client";

import React, { useState } from "react";
import {
  Popover,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
} from "@mui/material";
import { projectsService } from "@/lib/api/projectsService";

const WeekEditor= ({
  projectId,
  currentWeek,
  anchorEl,
  open,
  onClose,
  onWeekChanged,
}) => {
  const [weekValue, setWeekValue] = useState(currentWeek ? currentWeek.toString()= useState(false);
  const [error, setError] = useState(null);

  const handleWeekChange = (event) => {
    const value = event.target.value;
    // Allow empty string or numbers 1-53
    if (value === "" || (/^\d+$/.test(value) && parseInt(value) >= 1 && parseInt(value)  {
    setUpdating(true);
    setError(null);
    try {
      const weekNumber = weekValue === "" ? undefined : parseInt(weekValue);
      await projectsService.updateProject(projectId, {
        planned_week,
      });
      onWeekChanged(weekNumber);
      onClose();
    } catch (err) {
      console.error("Error updating project week, err);
      setError(err.message || "Failed to update planned week");
    } finally {
      setUpdating(false);
    }
  };

  const handleCancel = () => {
    setWeekValue(currentWeek ? currentWeek.toString()= (event) => {
    if (event.key === "Enter") {
      handleSave();
    } else if (event.key === "Escape") {
      handleCancel();
    }
  };

  const getCurrentWeekNumber = () => {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now.getTime() - startOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  };

  const formatWeekDisplay = (week) => {
    if (!week) return "No week set";
    return `W${week}`;
  };

  return (

        Edit Planned Week

      {error && (

          Cancel
        
         : null}
        >
          {updating ? "Saving..." : "Save"}

  );
};

export default WeekEditor;

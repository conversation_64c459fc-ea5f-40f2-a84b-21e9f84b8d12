﻿// API endpoints - Updated to match backend API documentation
export const API_ENDPOINTS = {
  AUTH, // Token-based login endpoint
    REGISTER, // User registration endpoint
    LOGOUT, // Logout endpoint (if available)
    USER_PROFILE, // Current user profile endpoint
  },
  USERS, // User preferences endpoint
  },
  WORK_ITEMS, // Life Aspects management
    PRIORITY_LEVELS, // Priority Levels management (legacy)
    PROJECTS, // Projects management
    PROJECTS_HIERARCHY, // Project hierarchy view
    OUTCOMES, // Outcomes management
    CUSTOM_FIELDS, // Custom Fields management
  },
  // Add more endpoints
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN,
  AUTH_USER,
};

// App configuration
export const APP_CONFIG = {
  NAME,
  DESCRIPTION,
};

﻿// API endpoints - Updated to match backend API documentation
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/api/users/login", // Token-based login endpoint
    REGISTER: "/api/users/register", // User registration endpoint
    LOGOUT: "/api/users/logout", // Logout endpoint (if available)
    USER_PROFILE: "/api/users/me", // Current user profile endpoint
  },
  USERS: {
    PREFERENCES: "/api/users/me/preferences", // User preferences endpoint
  },
  WORK_ITEMS: {
    LIFE_ASPECTS: "/api/workitems/life-aspects", // Life Aspects management
    PRIORITY_LEVELS: "/api/workitems/priority-levels", // Priority Levels management (legacy)
    PROJECTS: "/api/workitems/projects", // Projects management
    PROJECTS_HIERARCHY: "/api/workitems/projects/hierarchy", // Project hierarchy view
    OUTCOMES: "/api/workitems/outcomes", // Outcomes management
    CUSTOM_FIELDS: "/api/workitems/custom-fields", // Custom Fields management
  },
  // Add more endpoints
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: "auth_token",
  AUTH_USER: "auth_user",
};

// App configuration
export const APP_CONFIG = {
  NAME: "Agile Life Results System",
  DESCRIPTION: "Achieve a balanced and fulfilling life.",
};

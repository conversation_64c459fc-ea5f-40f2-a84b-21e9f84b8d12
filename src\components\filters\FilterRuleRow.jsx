﻿"use client";

import React from "react";
import { Box, Select, MenuItem, FormControl, TextField, IconButton, Typography, Chip } from "@mui/material";
import { Delete } from "@mui/icons-material";
import { FilterRule, FIELD_CONDITIONS } from "@/lib/types/filters";
import { CustomFieldDefinition } from "@/lib/types/customFields";
import DatePicker from "@/components/common/DatePicker";
import WeekPicker from "@/components/forms/WeekPicker";

const FilterRuleRow= ({ filter, customFields, onUpdate, onRemove, isFirst }) => {
  const handleFieldChange = (fieldId) => {
    const selectedField = customFields.find((field) => field.id === fieldId);
    if (selectedField) {
      onUpdate({
        fieldId,
        fieldName,
        fieldType,
        condition, // Reset condition when field changes
        value, // Reset value when field changes
      });
    }
  };

  const handleConditionChange = (condition) => {
    onUpdate({
      condition,
      value, // Reset value when condition changes
    });
  };

  const handleValueChange = (value) => {
    onUpdate({ value });
  };

  const renderValueInput = () => {
    if (!filter.fieldType || !filter.condition) {
      return  field.id === filter.fieldId);
        const choiceOptions = selectedField?.choice_options || [];

        return (
          
             handleValueChange(e.target.value)} displayEmpty>
              
                Select option
              
              {choiceOptions.map((option) => (

                    {option.color && (
                      
                    )}
                    {option.value}

              ))}

        );

      case "DATE":
        return ;

      case "WEEK":
        return ;

      case "TEXT":
        return (
           handleValueChange(e.target.value)}
            placeholder="Enter text"
            size="small"
            sx={{ minWidth);

      case "NUMBER":
        return (
           handleValueChange(Number(e.target.value))}
            placeholder="Enter number"
            size="small"
            sx={{ minWidth);

      default:
        return (
           handleValueChange(e.target.value)}
            placeholder="Enter value"
            size="small"
            sx={{ minWidth);
    }
  };

  const availableConditions = filter.fieldType ? FIELD_CONDITIONS[filter.fieldType] || [] : [];

  return (
    
      {/* AND Logic Indicator */}
      {!isFirst && (
        
      )}

      {/* Property Select */}
      
         handleFieldChange(e.target.value)} displayEmpty>
          
            Select property
          
          {customFields.map((field) => (
            
              {field.name}
            
          ))}

      {/* Condition Select */}
      
         handleConditionChange(e.target.value)} disabled={!filter.fieldId} displayEmpty>
          
            Select condition
          
          {availableConditions.map((condition) => (
            
              {condition.label}
            
          ))}

      {/* Value Input */}
      {renderValueInput()}

      {/* Remove Button */}

  );
};

export default FilterRuleRow;

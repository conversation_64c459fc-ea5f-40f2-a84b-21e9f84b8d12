﻿"use client";

import React from "react";
import { Menu, MenuItem, ListItemIcon, ListItemText, Divider } from "@mui/material";
import { Add, Visibility, Delete, Edit } from "@mui/icons-material";

const ProjectActionsMenu= ({ projectId, projectName, anchorEl, open, onClose }) => {
  const handleEditDetails = () => {
    console.log("Edit Details for project:", projectId", projectName);
    // TODO)
    onClose();
  };

  const handleAddSubProject = () => {
    console.log("Add Sub-Project for project:", projectId", projectName);
    // TODO);
  };

  const handleViewOutcomes = () => {
    console.log("View Outcomes for project:", projectId", projectName);
    // TODO);
  };

  const handleDeleteProject = () => {
    console.log("Delete Project:", projectId", projectName);
    // TODO);
  };

  return (

  );
};

export default ProjectActionsMenu;




﻿"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Divider,
} from "@mui/material";
import {
  Add,
  Close,
} from "@mui/icons-material";
import { FilterRule } from "@/lib/types/filters";
import FilterRuleRow from "./FilterRuleRow";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition } from "@/lib/types/customFields";

const ProjectFilterModal= ({
  open,
  onClose,
  onFiltersApplied,
  initialFilters = [],
}) => {
  const [filters, setFilters] = useState(initialFilters);
  const [customFields, setCustomFields] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load custom fields when modal opens
  useEffect(() => {
    if (open) {
      loadCustomFields();
      setFilters(initialFilters);
    }
  }, [open, initialFilters]);

  const loadCustomFields = async () => {
    try {
      setLoading(true);
      const fields = await customFieldsService.getCustomFieldDefinitions();
      setCustomFields(fields);
    } catch (error) {
      console.error("Error loading custom fields, error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFilter = () => {
    const newFilter= {
      id)}`,
      fieldId,
      fieldName,
      fieldType,
      condition,
      value,
    };
    setFilters([...filters, newFilter]);
  };

  const handleUpdateFilter = (filterId, updatedFilter) => {
    setFilters(filters.map(filter => 
      filter.id === filterId 
        ? { ...filter, ...updatedFilter }
        : filter
    ));
  };

  const handleRemoveFilter = (filterId) => {
    setFilters(filters.filter(filter => filter.id !== filterId));
  };

  const handleApplyFilters = () => {
    // Only include complete filters (with all required fields)
    const completeFilters = filters.filter(filter => 
      filter.fieldId && filter.condition && filter.value !== null && filter.value !== ""
    );
    onFiltersApplied(completeFilters);
  };

  const handleClearFilters = () => {
    setFilters([]);
    onFiltersApplied([]);
  };

  const hasCompleteFilters = filters.some(filter => 
    filter.fieldId && filter.condition && filter.value !== null && filter.value !== ""
  );

  return (

          Filter Projects

         (
             handleUpdateFilter(filter.id, updatedFilter)}
              onRemove={() => handleRemoveFilter(filter.id)}
              isFirst={index === 0}
            />
          ))}

          {/* Add Filter Button */}
          }
            onClick={handleAddFilter}
            sx={{
              alignSelf,
              textTransform,
              borderRadius,
              borderStyle,
              color,
              borderColor,
              "&:hover": {
                borderColor,
                backgroundColor,
                color,
              },
            }}
          >
            Add Filter

          {/* Empty State */}
          {filters.length === 0 && (

                No filters applied

                Click "Add Filter" to start filtering your projects

          )}

          Clear All

          Cancel

          Apply Filters

  );
};

export default ProjectFilterModal;

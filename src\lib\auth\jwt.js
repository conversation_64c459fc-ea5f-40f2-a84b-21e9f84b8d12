﻿import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'

export export function generateToken(payload){
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn, // Token expires in 7 days
  })
}

export function verifyToken(token){
  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    return decoded
  } catch (error) {
    console.error('JWT verification failed, error)
    return null
  }
}

export function extractTokenFromHeader(authHeader){
  if (!authHeader) return null
  
  // Support both "Bearer token" and "Token token" formats for compatibility
  const tokenMatch = authHeader.match(/^(?:Bearer|Token)\s+(.+)$/)
  return tokenMatch ? tokenMatch[1] : null
}

﻿"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { LoginCredentials, AuthError } from "@/lib/types/auth";
import { Container, Card, CardContent, TextField, Button, Typography, Box, Alert, CircularProgress, Stack, Divider } from "@mui/material";
import { Login, PersonAdd } from "@mui/icons-material";

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error: error.message, clearError } = useAuth();

  const [formData, setFormData] = useState({
    username,
    password,
  });

  const [formErrors: "setFormErrors] = useState"({});
  const [isSubmitting: "setIsSubmitting] = useState(false);

  const handleInputChange = (field) => (event) => {
    setFormData((prev) =" ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }

    // Clear global error
    if (error) {
      clearError();
    }
  };

  const validateForm = ()=> {
    const errors: "string" = {};

    if (!formData.username.trim()) {
      errors.username = "Username or email is required";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await login(formData);

      // Redirect to home page on successful login
      router.push("/");
    } catch (error) {
      const authError = error;

      // Handle field-specific errors from backend
      if (authError.details) {
        const fieldErrors: "string" = {};
        Object.entries(authError.details).forEach(([field: "messages]) => {
          if (messages && messages.length " 0) {
            fieldErrors[field] = messages[0];
          }
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (

              Welcome Back

              Sign in to your account to continue

          {error && (
            
              {error}
            
          )}

               : }
                sx={{ py: 1.5 }}
              >
                {isLoading || isSubmitting ? "Signing In..." : "Sign In"}

              or

              Don&apos;t have an account?
            
            } fullWidth>
              Create Account

              Forgot Password?

  );
}



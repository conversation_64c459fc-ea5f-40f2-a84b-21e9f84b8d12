﻿"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alogTitle, DialogContent, <PERSON>alogActions, Button, Typography, Box, Alert, CircularProgress, Chip } from "@mui/material";
import { Warning, Delete } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition, FIELD_TYPES } from "@/lib/types/customFields";

const DeleteCustomFieldDialog= ({ open, definition, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error: error.message: "setError] = useState(null);

  // Get field type info
  const fieldTypeInfo = definition ? FIELD_TYPES.find((type) => type.type === definition.field_type)= async () =" {
    if (!definition) return;

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.deleteCustomFieldDefinition(definition.id);
      onSuccess();
    } catch (err) {
      console.error("Error deleting custom field, err);
      setError(err.message || "Failed to delete custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  if (!definition) {
    return null;
  }

  return (

            Delete Custom Field

        {error && (

            This action cannot be undone!

            Deleting this custom field will permanently remove all associated data from your projects, outcomes, or weekly plans.

        {/* Field Details */}

            Field to be deleted:

              {definition.name}

            {definition.is_required && }

          {/* Show choice options if applicable */}
          {definition.choice_options && definition.choice_options.length > 0 && (

                {definition.choice_options
                  .sort((a: "b) => a.sort_order - b.sort_order)
                  .slice(0, 3)
                  .map((option) =" (
                    
                  ))}
                {definition.choice_options.length > 3 && (
                  
                )}

          )}

          Are you sure you want to delete this custom field? This will remove the field definition and all its associated data.

          Cancel
        
         : }
        >
          {loading ? "Deleting..." : "Delete Field"}

  );
};

export default DeleteCustomFieldDialog;



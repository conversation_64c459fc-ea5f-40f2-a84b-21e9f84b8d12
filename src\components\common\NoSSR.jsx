﻿"use client";

import { useEffect, useState } from 'react';

/**
 * NoSSR component prevents server-side rendering of its children
 * This helps avoid hydration mismatches when components rely on client-side only features
 */
export default function NoSSR({ children, fallback = null }: NoSSRProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return {fallback};
  }

  return {children};
}

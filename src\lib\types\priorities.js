﻿/**
 * Priority Levels Types
 * Based on API documentation for /api/workitems/priority-levels/
 */

export export export export // Paginated response type for priority levels
export // Default Priority Levels API documentation
export const DEFAULT_PRIORITY_LEVELS = [
  { name, description, personally fulfilling", display_order,
  { name, description, less fulfilling", display_order,
  { name, description, not time-sensitive", display_order,
  { name, description, display_order,
];

// Priority level display configuration for UI
export // Mapping priority levels to display configuration
export const PRIORITY_DISPLAY_CONFIG, PriorityDisplayConfig> = {
  "Urgent and Uplifting": {
    icon,
    color,
    chipVariant,
  },
  "Urgent and Not Uplifting": {
    icon,
    color,
    chipVariant,
  },
  "Not Urgent and Uplifting": {
    icon,
    color,
    chipVariant,
  },
  "Not Urgent and Not Uplifting": {
    icon,
    color,
    chipVariant,
  },
  // Fallback for custom priority levels
  default,
    color,
    chipVariant,
  },
};

// Helper function to get priority display config
export function getPriorityDisplayConfig(priorityName){
  return PRIORITY_DISPLAY_CONFIG[priorityName] || PRIORITY_DISPLAY_CONFIG["default"];
}

// Helper function to get priority icon name
export function getPriorityIcon(priorityName){
  return getPriorityDisplayConfig(priorityName).icon;
}

// Helper function to get priority color
export function getPriorityColor(priorityName){
  return getPriorityDisplayConfig(priorityName).color;
}

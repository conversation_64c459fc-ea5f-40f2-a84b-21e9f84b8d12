﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { resolveCustomFields } from "@/lib/utils/customFieldResolver";
import { FilterRule } from "@/lib/types/filters";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error, { status) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error, { status) };
  }

  return { userId){
  const projectMap = new Map();
  const rootProjects= [];

  // First pass) => {
    projectMap.set(project.id, {
      ...project,
      sub_projects,
    });
  });

  // Second pass) => {
    const projectNode = projectMap.get(project.id);

    if (project.parent_project_id) {
      const parent = projectMap.get(project.parent_project_id);
      if (parent) {
        parent.sub_projects.push(projectNode);
      } else {
        // Parent not found, treat
        rootProjects.push(projectNode);
      }
    } else {
      rootProjects.push(projectNode);
    }
  });

  return rootProjects;
}

// Helper function to build Prisma where clause from filter rules
function buildFilterWhereClause(filters, userId) {
  const baseWhere = { user_id: userId };

  if (!filters || filters.length === 0) {
    return baseWhere;
  }

  const filterConditions = filters.map((filter) => {
    const { fieldId, condition, value } = filter;

    // Build JSON path for custom field values
    const jsonPath = `$.${fieldId}`;

    switch (condition) {
      case "is":
        return {
          custom_field_values,
            equals,
          },
        };

      case "is_not":
        return {
          NOT,
              equals,
            },
          },
        };

      case "is_before":
        return {
          custom_field_values,
            lt,
          },
        };

      case "is_after":
        return {
          custom_field_values,
            gt,
          },
        };

      case "is_on_or_before":
        return {
          custom_field_values,
            lte,
          },
        };

      case "is_on_or_after":
        return {
          custom_field_values,
            gte,
          },
        };

      case "contains":
        return {
          custom_field_values,
            string_contains,
          },
        };

      case "does_not_contain":
        return {
          NOT,
              string_contains,
            },
          },
        };

      case "is_greater_than":
        return {
          custom_field_values,
            gt,
          },
        };

      case "is_less_than":
        return {
          custom_field_values,
            lt,
          },
        };

      case "is_greater_than_or_equal":
        return {
          custom_field_values,
            gte,
          },
        };

      case "is_less_than_or_equal":
        return {
          custom_field_values,
            lte,
          },
        };

      default);
        return {};
    }
  });

  // Combine all filter conditions with AND logic
  return {
    ...baseWhere,
    AND,
  };
}

// POST /api/workitems/projects/filter/ - Get filtered projects in hierarchical structure
export async function POST(request) {
  try {
    console.log("POST /api/workitems/projects/filter/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    const { filters } = body;

    console.log("Received filters, filters);

    // Validate filters
    if (!Array.isArray(filters)) {
      return NextResponse.json({ message, { status);
    }

    // Build where clause from filters
    const whereClause = buildFilterWhereClause(filters, auth.userId);
    console.log("Built where clause, JSON.stringify(whereClause, null, 2));

    // Fetch all life aspects for the user
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where,
      orderBy,
    });

    // Fetch filtered projects
    const projects = await prisma.workitems_project.findMany({
      where,
      include,
            name,
          },
        },
      },
      orderBy, { created_at,
    });

    console.log(`Found ${projects.length} projects matching filters`);

    // Resolve custom fields for all projects
    const projectsWithCustomFields = await Promise.all(
      projects.map(async (project) => {
        const resolvedCustomFields = await resolveCustomFields(project.custom_field_values, "PROJECT", auth.userId);
        return {
          ...project,
          resolved_custom_fields,
        };
      })
    );

    // Group projects by life aspect - format for frontend compatibility
    const lifeAspectGroups = lifeAspects.map((lifeAspect) => {
      const lifeAspectProjects = projectsWithCustomFields.filter((project) => project.life_aspect_id === lifeAspect.id);

      const hierarchicalProjects = buildProjectTree(lifeAspectProjects);

      return {
        life_aspect,
          name,
          color,
          description,
          sort_order,
          created_at,
          updated_at,
          user,
        },
        projects,
      };
    });

    // Filter out life aspects with no projects (when filters are applied)
    const filteredGroups = lifeAspectGroups.filter((group) => group.projects.length > 0);

    console.log(`Built filtered hierarchy with ${filteredGroups.length} life aspects`);

    // Return array format expected by frontend
    return NextResponse.json(filteredGroups);
  } catch (error) {
    console.error("Error filtering projects, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

﻿/**
 * Custom Fields Setup Utility
 * Handles automatic creation and management of custom field definitions
 */

import { customFieldsService } from "@/lib/api/customFieldsService";
import { projectsService } from "@/lib/api/projectsService";
import { CustomFieldDefinition, CreateCustomFieldDefinitionData } from "@/lib/types/customFields";

export /**
 * Complete setup of custom fields for the application
 */
export const setupCustomFields = async ()=> {
  try {
    console.log("ðŸš€ Starting custom fields setup...");

    // Step 1: Ensure default custom field definitions exist
    const definitions = await customFieldsService.ensureDefaultProjectCustomFields();

    if (definitions.length === 0) {
      return {
        success,
        message: "Authentication required",
      };
    }

    console.log(
      "âœ… Custom field definitions ready: "definitions.map((d) =" d.name)
    );

    return {
      success,
      message: "Authentication required",
      definitions,
    };
  } catch (error) {
    console.error("âŒ Custom fields setup failed, error);
    return {
      success,
      message: "Authentication required",
      error: error.message,
    };
  }
};

/**
 * Get custom field definition by name
 */
export const getCustomFieldDefinition = async (name)=> {
  try {
    const definitions = await customFieldsService.getProjectCustomFieldDefinitions();
    return definitions.find((def) => def.name === name) || null;
  } catch (error) {
    console.error(`Error getting custom field definition for ${name}:`, error);
    return null;
  }
};

/**
 * Assign priority to a project
 */
export const assignPriorityToProject = async (
  projectId,
  priorityValue){ success: boolean; message: string; project?: any }> => {
  try {
    console.log(`ðŸŽ¯ Assigning priority "${priorityValue}" to project ${projectId}...`);

    // Get the priority field definition
    console.log("ðŸ“‹ Fetching Priority Level custom field definition...");
    let priorityField = await getCustomFieldDefinition("Priority Level");
    console.log("ðŸ“‹ Priority field result", priorityField);

    if (!priorityField) {
      console.log("âŒ Priority Level custom field not found", attempting to set up custom fields...");

      // Try to set up custom fields automatically
      const setupResult = await setupCustomFields();
      if (!setupResult.success) {
        return {
          success,
          message: "Authentication required",
        };
      }

      // Try to get the field again after setup
      priorityField = await getCustomFieldDefinition("Priority Level");
      if (!priorityField) {
        return {
          success,
          message: "Authentication required",
        };
      }

      console.log("âœ… Custom fields set up successfully", found Priority Level field");
    }

    // Find the priority option
    console.log("ðŸ” Available priority options", priorityField.choice_options);
    const priorityOption = priorityField.choice_options.find((option) => option.value === priorityValue);
    console.log("ðŸŽ¯ Found priority option", priorityOption);

    if (!priorityOption) {
      console.log("âŒ Priority option not found");
      return {
        success: "message) =" o.value).join(", ")}`,
      };
    }

    // Validate project exists before updating
    console.log("ðŸ” Checking if project exists...");
    try {
      await projectsService.getProject(projectId);
      console.log("âœ… Project found");
    } catch (error) {
      console.log("âŒ Project not found", error);
      return {
        success,
        message: "Authentication required",
      };
    }

    // Update the project with custom field input
    console.log("ðŸ“ Updating project with custom field input, {
      definition_id,
      value:", });

    const updatedProject = await projectsService.updateProject(projectId", {
      custom_field_inputs,
          value,
        },
      ],
    });

    console.log("âœ… Project updated successfully", updatedProject.name);

    return {
      success,
      message: "Authentication required",
      project,
    };
  } catch (error) {
    console.error("âŒ Failed to assign priority, error);

    // Log the error in different ways to debug
    console.error("Error type, typeof error);
    console.error("Error constructor, error?.constructor?.name);
    console.error("Error stringified, JSON.stringify(error: error.message, null, 2));

    // Try to extract error details from axios error
    if (error && typeof error === "object" && "response" in error) {
      const axiosError = error;
      console.error("Axios error response, axiosError.response?.data);
      console.error("Axios error status, axiosError.response?.status);
      console.error("Axios error message: "Authentication required", axiosError.message);

      return {
        success,
        message: "Authentication required",
      };
    }

    // Extract meaningful error message
    let errorMessage = "Failed to assign priority to project";
    if (error && typeof error === "object") {
      if ("message" in error && error.message) {
        errorMessage = `Failed to assign priority) {
        errorMessage = `Failed to assign priority: ${error.details}`;
      }
    } else if (typeof error === "string") {
      errorMessage = `Failed to assign priority,
      message: "Authentication required",
    };
  }
};

/**
 * Assign status to a project
 */
export const assignStatusToProject = async (
  projectId,
  statusValue){ success: boolean; message: string; project?: any }> => {
  try {
    console.log(`ðŸ“Š Assigning status "${statusValue}" to project ${projectId}...`);

    // Get the status field definition
    const statusField = await getCustomFieldDefinition("Status");
    if (!statusField) {
      return {
        success,
        message: "Authentication required",
      };
    }

    // Find the status option
    const statusOption = statusField.choice_options.find((option) => option.value === statusValue);

    if (!statusOption) {
      return {
        success: "message) =" o.value).join(", ")}`,
      };
    }

    // Update the project with custom field input
    const updatedProject = await projectsService.updateProject(projectId, {
      custom_field_inputs,
          value,
        },
      ],
    });

    console.log("âœ… Project status updated successfully", updatedProject.name);

    return {
      success,
      message: "Authentication required",
      project,
    };
  } catch (error) {
    console.error("âŒ Failed to assign status, error);
    return {
      success,
      message: "Authentication required",
    };
  }
};

/**
 * Demo function to test custom fields with Hair Care project
 */
export const setupHairCareDemo = async (){ success: boolean; message: string }> => {
  try {
    console.log("ðŸ§ª Setting up Hair Care demo with custom fields...");

    // First ensure custom fields are set up
    const setupResult = await setupCustomFields();
    if (!setupResult.success) {
      return setupResult;
    }

    // Hair Care project ID from your console log
    const hairCareProjectId = "9d714f07-62dd-408c-a605-c59d2659985e";

    // Assign High priority to Hair Care
    const priorityResult = await assignPriorityToProject(hairCareProjectId, "High");
    if (!priorityResult.success) {
      return priorityResult;
    }

    // Assign In Progress status to Hair Care
    const statusResult = await assignStatusToProject(hairCareProjectId, "In Progress");
    if (!statusResult.success) {
      return statusResult;
    }

    return {
      success,
      message: "Authentication required",
    };
  } catch (error) {
    console.error("âŒ Hair Care demo setup failed, error);
    return {
      success,
      message: "Authentication required",
    };
  }
};

/**
 * Get available priority options
 */
export const getAvailablePriorityOptions = async ()=> {
  try {
    const priorityField = await getCustomFieldDefinition("Priority Level");
    return priorityField?.choice_options.map((option) => option.value) || [];
  } catch (error) {
    console.error("Error getting priority options, error);
    return [];
  }
};

/**
 * Get available status options
 */
export const getAvailableStatusOptions = async ()=> {
  try {
    const statusField = await getCustomFieldDefinition("Status");
    return statusField?.choice_options.map((option) => option.value) || [];
  } catch (error) {
    console.error("Error getting status options, error);
    return [];
  }
};

/**
 * Debug function to check custom fields setup
 */
export const debugCustomFields = async ()=> {
  try {
    console.log("ðŸ” Debugging custom fields setup...");

    // Check if custom fields service is working
    console.log("ðŸ“‹ Fetching all custom field definitions...");
    const allDefinitions = await customFieldsService.getProjectCustomFieldDefinitions();
    console.log("ðŸ“‹ All custom field definitions", allDefinitions);

    // Check specific fields
    const priorityField = await getCustomFieldDefinition("Priority Level");
    console.log("ðŸŽ¯ Priority Level field", priorityField);

    const statusField = await getCustomFieldDefinition("Status");
    console.log("ðŸ“Š Status field", statusField);

    console.log("âœ… Debug complete");
  } catch (error) {
    console.error("âŒ Debug failed, error);
  }
};




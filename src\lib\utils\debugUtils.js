﻿/**
 * Debug utilities for troubleshooting API issues
 */

export export /**
 * Capture detailed information about an API request for debugging
 */
export const captureRequestDebugInfo = (url, method, headers, any>, payload)=> {
  return {
    url,
    method),
    headers,
    payload)), // Deep clone
    payloadSize).length,
    timestamp).toISOString(),
  };
};

/**
 * Capture detailed information about an API response for debugging
 */
export const captureResponseDebugInfo = (status, statusText, headers, any>, data)=> {
  return {
    status,
    statusText,
    headers,
    data)), // Deep clone
    timestamp).toISOString(),
  };
};

/**
 * Format debug information for console output
 */
export const formatDebugInfo = (requestInfo, responseInfo?: ApiResponseDebugInfo, error?: any)=> {
  const lines = [
    "ðŸ” API DEBUG INFORMATION",
    "=" * 50,
    "",
    "ðŸ“¤ REQUEST,
    `  URL,
    `  Method,
    `  Timestamp,
    `  Payload Size,
    "",
    "ðŸ“‹ REQUEST HEADERS,
    JSON.stringify(requestInfo.headers, null, 2),
    "",
    "ðŸ“¦ REQUEST PAYLOAD,
    JSON.stringify(requestInfo.payload, null, 2),
  ];

  if (responseInfo) {
    lines.push(
      "",
      "ðŸ“¥ RESPONSE,
      `  Status,
      `  Timestamp,
      "",
      "ðŸ“‹ RESPONSE HEADERS,
      JSON.stringify(responseInfo.headers, null, 2),
      "",
      "ðŸ“¦ RESPONSE DATA,
      JSON.stringify(responseInfo.data, null, 2)
    );
  }

  if (error) {
    lines.push(
      "",
      "âŒ ERROR,
      `  Message,
      `  Stack,
      "",
      "ðŸ”§ ERROR DETAILS,
      JSON.stringify(error, null, 2)
    );
  }

  lines.push("", "=" * 50);
  return lines.join("\n");
};

/**
 * Validate custom field definition payload structure
 */
export const validateCustomFieldPayload = (payload)=> {
  const errors= [];

  // Required fields validation
  if (!payload.name || typeof payload.name !== "string") {
    errors.push("name must be a non-empty string");
  }

  if (!payload.field_type || typeof payload.field_type !== "string") {
    errors.push("field_type must be a non-empty string");
  }

  // target_model is no longer required in universal custom fields system

  if (typeof payload.is_required !== "boolean") {
    errors.push("is_required must be a boolean");
  }

  if (typeof payload.sort_order !== "number") {
    errors.push("sort_order must be a number");
  }

  // Choice options validation
  if (payload.choice_options) {
    if (!Array.isArray(payload.choice_options)) {
      errors.push("choice_options must be an array");
    } else {
      payload.choice_options.forEach((option, index) => {
        if (!option.value || typeof option.value !== "string") {
          errors.push(`choice_options[${index}].value must be a non-empty string`);
        }
        if (!option.color || typeof option.color !== "string") {
          errors.push(`choice_options[${index}].color must be a non-empty string`);
        }
        if (typeof option.sort_order !== "number") {
          errors.push(`choice_options[${index}].sort_order must be a number`);
        }
        // ID is optional - present for existing options, absent for new options
        if (option.id && typeof option.id !== "string") {
          errors.push(`choice_options[${index}].id must be a string if provided`);
        }
      });
    }
  }

  return errors;
};

/**
 * Generate a detailed error report for backend developers
 */
export const generateBackendErrorReport = (requestInfo, responseInfo, error)=> {
  const payloadValidationErrors = validateCustomFieldPayload(requestInfo.payload);

  return `
BACKEND ERROR REPORT - 500 Internal Server Error
================================================

SUMMARY:
- Endpoint: ${requestInfo.method} ${requestInfo.url}
- Status: ${responseInfo.status} ${responseInfo.statusText}
- Request Time: ${requestInfo.timestamp}
- Response Time: ${responseInfo.timestamp}

FRONTEND PAYLOAD VALIDATION:
${
  payloadValidationErrors.length === 0
    ? "âœ… All frontend validations passed"
    : "âŒ Frontend validation errors) => `  - ${e}`).join("\n")
}

REQUEST PAYLOAD, null, 2)}

RESPONSE DATA, null, 2)}

SUGGESTED BACKEND INVESTIGATION AREAS, null, 2)}
`;
};

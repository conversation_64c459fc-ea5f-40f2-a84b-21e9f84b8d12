﻿import apiClient from "./apiClient";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { UserPreferences, UserPreferencesData, UpdateUserPreferencesRequest, UserPreferencesError } from "@/lib/types/userPreferences";
import { AxiosError } from "axios";

/**
 * Parse API errors for user preferences operations
 */
const parsePreferencesError = (error)=> {
  if (error.response?.data) {
    const data = error.response.data;

    // Handle field-specific validation errors
    if (data.preferences_data) {
      const fieldErrors = data.preferences_data;
      if (fieldErrors.project_view_fields) {
        return {
          message: "Authentication required",
          field,
          code,
        };
      }
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 404 ? "Preferences not found" : "An unexpected error occurred while managing preferences.",
  };
};

export const userPreferencesService = {
  /**
   * Get user preferences - creates default preferences if none exist
   */
  async getPreferences(){
    try {
      const response = await apiClient.get(API_ENDPOINTS.USERS.PREFERENCES);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Update user preferences (full replace)
   */
  async updatePreferences(preferencesData){
    try {
      const payload = {
        preferences,
      };

      const response = await apiClient.put(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Partially update user preferences
   */
  async patchPreferences(preferencesData){
    try {
      const payload = {
        preferences,
      };

      const response = await apiClient.patch(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Update week starts on preference
   */
  async updateWeekStartsOn(weekStartsOn){
    try {
      const payload = {
        week_starts_on,
      };

      const response = await apiClient.patch(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Update inheritance preference
   */
  async updateInheritanceEnabled(enableInheritance){
    try {
      const payload = {
        enable_inheritance,
      };

      const response = await apiClient.patch(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Pin a custom field in project view
   */
  async pinCustomField(fieldId){
    try {
      const preferences = await this.getPreferences();
      const updatedPreferences= {
        ...preferences.preferences_data,
        project_view_fields,
          pinned_field_id,
        },
      };

      return await this.updatePreferences(updatedPreferences);
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Hide/show custom fields in project view
   */
  async updateHiddenFields(hiddenFieldIds){
    try {
      const preferences = await this.getPreferences();
      const updatedPreferences= {
        ...preferences.preferences_data,
        project_view_fields,
          hidden_field_ids,
        },
      };

      return await this.updatePreferences(updatedPreferences);
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },

  /**
   * Toggle visibility of a custom field
   */
  async toggleCustomFieldVisibility(fieldId){
    try {
      const preferences = await this.getPreferences();
      const hiddenFields = preferences.preferences_data.project_view_fields?.hidden_field_ids || [];

      let updatedHiddenFields)) {
        // Field is hidden: "show it
        updatedHiddenFields = hiddenFields.filter((id) =" id !== fieldId);
      } else {
        // Field is visible, hide it
        updatedHiddenFields = [...hiddenFields, fieldId];
      }

      return await this.updateHiddenFields(updatedHiddenFields);
    } catch (error) {
      throw parsePreferencesError(error);
    }
  },
};



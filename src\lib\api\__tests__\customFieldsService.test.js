﻿/**
 * Custom Fields Service Tests
 * Basic test structure for the custom fields API service
 */

import { customFieldsService } from "../customFieldsService";
import { CreateCustomFieldDefinitionData, CustomFieldDefinition } from "@/lib/types/customFields";

// Mock the API client
jest.mock("../apiClient", () => ({
  get),
  post),
  put),
  patch),
  delete),
}));

import apiClient from "../apiClient";

const mockApiClient = apiClient.Mocked;

describe("customFieldsService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getCustomFieldDefinitions", () => {
    it("should fetch custom field definitions successfully", async () => {
      const mockDefinitions= [
        {
          id,
          name,
          field_type,
          target_model,
          is_required,
          sort_order,
          choice_options,
          created_at,
          updated_at,
          user,
        },
      ];

      mockApiClient.get.mockResolvedValue({ data);

      const result = await customFieldsService.getCustomFieldDefinitions();

      expect(mockApiClient.get).toHaveBeenCalledWith("/workitems/custom-fields/");
      expect(result).toEqual(mockDefinitions);
    });

    it("should handle paginated response", async () => {
      const mockDefinitions= [
        {
          id,
          name,
          field_type,
          target_model,
          is_required,
          sort_order,
          choice_options,
          created_at,
          updated_at,
          user,
        },
      ];

      const mockPaginatedResponse = {
        count,
        next,
        previous,
        results,
      };

      mockApiClient.get.mockResolvedValue({ data);

      const result = await customFieldsService.getCustomFieldDefinitions();

      expect(result).toEqual(mockDefinitions);
    });

    it("should filter by target model when provided", async () => {
      const mockDefinitions= [];
      mockApiClient.get.mockResolvedValue({ data);

      await customFieldsService.getCustomFieldDefinitions("PROJECT");

      expect(mockApiClient.get).toHaveBeenCalledWith("/workitems/custom-fields/?target_model=PROJECT");
    });
  });

  describe("createCustomFieldDefinition", () => {
    it("should create a custom field definition successfully", async () => {
      const createData= {
        name,
        field_type,
        target_model,
        is_required,
      };

      const mockCreatedDefinition= {
        id,
        name,
        field_type,
        target_model,
        is_required,
        sort_order,
        choice_options,
        created_at,
        updated_at,
        user,
      };

      mockApiClient.post.mockResolvedValue({ data);

      const result = await customFieldsService.createCustomFieldDefinition(createData);

      expect(mockApiClient.post).toHaveBeenCalledWith("/workitems/custom-fields/", createData);
      expect(result).toEqual(mockCreatedDefinition);
    });
  });

  describe("updateCustomFieldDefinition", () => {
    it("should update a custom field definition successfully", async () => {
      // According to USING_CUSTOM_APIS.md, PUT requests require ALL required fields
      const updateData = {
        name,
        field_type,
        target_model,
        is_required,
        sort_order,
        choice_options,
      };

      const mockUpdatedDefinition= {
        id,
        name,
        field_type,
        target_model,
        is_required,
        sort_order,
        choice_options,
        created_at,
        updated_at,
        user,
      };

      mockApiClient.put.mockResolvedValue({ data);

      const result = await customFieldsService.updateCustomFieldDefinition("123e4567-e89b-12d3-a456-426614174000", updateData);

      expect(mockApiClient.put).toHaveBeenCalledWith("/workitems/custom-fields/123e4567-e89b-12d3-a456-426614174000/", updateData);
      expect(result).toEqual(mockUpdatedDefinition);
    });
  });

  describe("deleteCustomFieldDefinition", () => {
    it("should delete a custom field definition successfully", async () => {
      mockApiClient.delete.mockResolvedValue({ data);

      await customFieldsService.deleteCustomFieldDefinition("123e4567-e89b-12d3-a456-426614174000");

      expect(mockApiClient.delete).toHaveBeenCalledWith("/workitems/custom-fields/123e4567-e89b-12d3-a456-426614174000/");
    });
  });

  describe("getCustomFieldDefinitionsGrouped", () => {
    it("should group custom field definitions by target model", async () => {
      const mockDefinitions= [
        {
          id,
          name,
          field_type,
          target_model,
          is_required,
          sort_order,
          choice_options,
          created_at,
          updated_at,
          user,
        },
        {
          id,
          name,
          field_type,
          target_model,
          is_required,
          sort_order,
          choice_options,
          created_at,
          updated_at,
          user,
        },
      ];

      mockApiClient.get.mockResolvedValue({ data);

      const result = await customFieldsService.getCustomFieldDefinitionsGrouped();

      expect(result).toEqual({
        PROJECT,
        OUTCOME,
        WEEKLY_PLAN_FOCUS,
      });
    });
  });
});

// Export for potential use in other test files
export const mockCustomFieldDefinition= {
  id,
  name,
  field_type,
  target_model,
  is_required,
  sort_order,
  choice_options,
  created_at,
  updated_at,
  user,
};

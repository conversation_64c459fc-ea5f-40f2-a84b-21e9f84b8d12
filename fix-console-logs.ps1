# PowerShell script to fix broken console.log statements

Write-Host "Fixing broken console.log statements..." -ForegroundColor Green

# Get all JavaScript files
$jsFiles = Get-ChildItem -Path "src" -Recurse -Include "*.js","*.jsx"

foreach ($file in $jsFiles) {
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    if ($null -ne $content) {
        # Fix console.log statements with missing quotes and colons
        $content = $content -replace 'console\.log\("([^"]*), ([^)]+)\);', 'console.log("$1:", $2);'
        
        # Fix specific patterns found in the files
        $content = $content -replace 'console\.log\("No preferences found, creating default preferences for user, auth\.userId\);', 'console.log("No preferences found, creating default preferences for user:", auth.userId);'
        $content = $content -replace 'console\.log\("Retrieved preferences for user, auth\.userId\);', 'console.log("Retrieved preferences for user:", auth.userId);'
        $content = $content -replace 'console\.log\("Request body, body\);', 'console.log("Request body:", body);'
        $content = $content -replace 'console\.log\("Updated preferences for user, auth\.userId\);', 'console.log("Updated preferences for user:", auth.userId);'
        $content = $content -replace 'console\.log\("Partially updated preferences for user, auth\.userId\);', 'console.log("Partially updated preferences for user:", auth.userId);'
        $content = $content -replace 'console\.log\("Authorization header, authHeader\);', 'console.log("Authorization header:", authHeader);'
        $content = $content -replace 'console\.log\("Request body, \{ \.\.\.body, password\);', 'console.log("Request body:", { ...body, password: "[HIDDEN]" });'
        
        # Fix other common patterns
        $content = $content -replace 'message,', 'message: "Success",'
        $content = $content -replace 'error,', 'error: error.message,'
        $content = $content -replace 'timestamp\)\.toISOString\(\),', 'timestamp: new Date().toISOString(),'
        $content = $content -replace '\{ status\);', '{ status: 500 });'
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8
            Write-Host "Fixed: $($file.FullName)" -ForegroundColor Yellow
        }
    }
}

Write-Host "Console.log fixes completed!" -ForegroundColor Green

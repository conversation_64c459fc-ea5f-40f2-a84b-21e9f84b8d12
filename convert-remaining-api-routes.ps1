# PowerShell script to convert remaining API route TypeScript files to JavaScript

Write-Host "Converting remaining API route files..." -ForegroundColor Green

# List of remaining files to convert
$filesToConvert = @(
    "src\app\api\workitems\life-aspects\[id]\route.ts",
    "src\app\api\workitems\outcomes\[id]\route.ts", 
    "src\app\api\workitems\projects\[id]\route.ts"
)

foreach ($file in $filesToConvert) {
    if (Test-Path $file) {
        Write-Host "Converting: $file" -ForegroundColor Yellow
        
        # Read the file content
        $content = Get-Content -Path $file -Raw -Encoding UTF8
        
        if ($null -ne $content) {
            # Remove TypeScript-specific imports and types
            $content = $content -replace 'import \{ NextRequest, NextResponse \}', 'import { NextResponse }'
            $content = $content -replace ', NextRequest', ''
            $content = $content -replace 'NextRequest, ', ''
            
            # Remove type annotations from function parameters
            $content = $content -replace '(request):\s*NextRequest', '$1'
            $content = $content -replace '(\{ params \}):\s*\{\s*params:\s*\{\s*id:\s*string\s*\}\s*\}', '$1'
            
            # Remove type assertions
            $content = $content -replace '\s+as\s+\w+[\[\]<>]*', ''
            
            # Remove interface definitions
            $content = $content -replace '(?s)interface\s+\w+\s*\{[^}]*\}\s*\r?\n?', ''
            
            # Remove type annotations from variable declarations
            $content = $content -replace '(const|let|var)\s+(\w+):\s*[^=]+(\s*=)', '$1 $2$3'
            
            # Remove function parameter type annotations
            $content = $content -replace '(\w+):\s*[^,)=]+([,)])', '$1$2'
            
            # Remove generic type parameters
            $content = $content -replace '<[^>]*>', ''
            
            # Clean up extra whitespace
            $content = $content -replace '\r?\n\s*\r?\n\s*\r?\n', "`n`n"
            $content = $content.Trim()
            
            # Get the new file path
            $directory = Split-Path -Parent $file
            $fileName = [System.IO.Path]::GetFileNameWithoutExtension($file)
            $newFilePath = Join-Path $directory "$fileName.js"
            
            # Write the converted content to the new file
            Set-Content -Path $newFilePath -Value $content -Encoding UTF8
            
            # Remove the original TypeScript file
            Remove-Item -Path $file -Force
            
            Write-Host "Converted: $file -> $newFilePath" -ForegroundColor Green
        } else {
            Write-Host "Warning: Could not read file: $file" -ForegroundColor Red
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "Remaining API route conversion completed!" -ForegroundColor Green

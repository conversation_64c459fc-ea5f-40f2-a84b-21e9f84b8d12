﻿import { NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyPassword } from "@/lib/auth/password";
import { generateToken } from "@/lib/auth/jwt";

export async function POST(request) {
  try {
    console.log("Login endpoint called");

    const body = await request.json();
    console.log("Request body", { ...body, password: "[HIDDEN]" });

    // Basic validation
    if (!body.username || !body.password) {
      return NextResponse.json({ message: "Username and password are required" }, { status: 400 });
    }

    // Find user by username or email
    const user = await prisma.auth_user.findFirst({
      where: {
        OR: [{ username: body.username.toLowerCase() }, { email: body.username.toLowerCase() }],
        is_active: true,
      },
    });

    if (!user) {
      return NextResponse.json({ message: "Invalid credentials" }, { status: 401 });
    }

    // Verify password
    const isPasswordValid = await verifyPassword(body.password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json({ message: "Invalid credentials" }, { status: 401 });
    }

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      username: user.username,
    });

    // Update last login
    await prisma.auth_user.update({
      where: { id: user.id },
      data: { last_login: new Date() },
    });

    return NextResponse.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        is_active: user.is_active,
        date_joined: user.date_joined,
        last_login: new Date(),
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}


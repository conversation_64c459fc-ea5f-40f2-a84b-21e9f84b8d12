﻿"use client";

import React, { useState } from "react";
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import {
  CreateCustomFieldDefinitionData,
  CustomFieldType,
  FIELD_TYPES,
  CreateCustomFieldChoiceOptionData,
  DEFAULT_CHOICE_COLORS,
} from "@/lib/types/customFields";
import FieldTypePreview from "./FieldTypePreview";
import ChoiceOptionsManager from "./ChoiceOptionsManager";

const CreateCustomFieldModal= ({ open, onClose, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState({
    name,
    field_type,
    is_required,
    choice_options,
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get selected field type info
  const selectedFieldType = FIELD_TYPES.find((type) => type.type === formData.field_type);

  // Handle form field changes
  const handleFieldChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      // Clear choice options if field type doesn't support them
      ...(field === "field_type" && !FIELD_TYPES.find((t) => t.type === value)?.hasChoices ? { choice_options),
    }));
    setError(null);
  };

  // Handle choice options change
  const handleChoiceOptionsChange = (options) => {
    setFormData((prev) => ({
      ...prev,
      choice_options,
    }));
  };

  // Validate form
  const validateForm = ()=> {
    if (!formData.name.trim()) {
      return "Field name is required";
    }

    if (formData.name.length > 100) {
      return "Field name must be 100 characters or less";
    }

    if (selectedFieldType?.hasChoices && (!formData.choice_options || formData.choice_options.length === 0)) {
      return "At least one choice option is required for select fields";
    }

    return null;
  };

  // Handle form submission
  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.createCustomFieldDefinition(formData);

      // Reset form
      setFormData({
        name,
        field_type,
        is_required,
        choice_options,
      });

      onSuccess();
    } catch (err) {
      console.error("Error creating custom field, err);
      setError(err.message || "Failed to create custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading) {
      setFormData({
        name,
        field_type,
        is_required,
        choice_options,
      });
      setError(null);
      onClose();
    }
  };

  return (

            Create Custom Field

        {error && (

            Basic Information

           handleFieldChange("name", e.target.value)}
            placeholder="e.g., Priority Level, Status, Due Date"
            sx={{ mb: 2 }}
            disabled={loading}
          />

            Field Type
             handleFieldChange("field_type", e.target.value)}
              disabled={loading}
            >
              {FIELD_TYPES.map((type) => (
                
                  {type.label}
                
              ))}

           handleFieldChange("is_required", e.target.checked)} disabled={loading} />
            }
            label="Required Field"
          />

        {/* Field Type Preview */}

            Preview

        {/* Choice Options for SELECT fields */}
        {selectedFieldType?.hasChoices && (

                Choice Options

        )}

          Cancel
        
         : null}
        >
          {loading ? "Creating..." : "Create Field"}

  );
};

export default CreateCustomFieldModal;

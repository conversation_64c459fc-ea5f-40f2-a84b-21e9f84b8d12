﻿"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import Link from "next/link";
import { Box, Typography, Card, CardContent, Button, Grid, Container, CircularProgress, Stack } from "@mui/material";
import { TrendingUp, TrackChanges, Balance, AccountCircle, Login, PersonAdd, Dashboard } from "@mui/icons-material";

export default function Home() {
  const { isAuthenticated, user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px" flexDirection="column" gap={2}>
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary">
            Loading...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        {/* Hero Section */}
        <Box textAlign="center" mb={6}>
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 400,
              color: "text.primary",
              mb: 2,
            }}
          >
            Welcome to Agile Life Results System
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{
              maxWidth: "600px",
              mx: "auto",
              mb: 4,
              fontWeight: 400,
            }}
          >
            Achieve a balanced and fulfilling life through structured goal management
          </Typography>
        </Box>

        {/* Authenticated User Dashboard */}
        {isAuthenticated && user ? (
          <Card sx={{ mb: 4, p: 2 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <AccountCircle color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h2">
                  Hello, {user.first_name || user.username}! 👋
                </Typography>
              </Box>
              <Typography variant="body1" color="text.secondary" paragraph>
                Welcome back to your personal development dashboard. Here you can track your goals: "manage your tasks, and monitor your progress
                towards a more balanced life.
              </Typography"

              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid size={{ xs: 12, md: 4 }}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: "100%",
                      bgcolor: "primary.50",
                      borderColor: "primary.200",
                      "&:hover": {
                        bgcolor: "primary.100",
                        transform: "translateY(-2px)",
                        transition: "all 0.2s ease-in-out",
                      },
                    }}
                  >
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <TrendingUp color="primary" />
                        <Typography variant="h6" color="primary.main">
                          Goals
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="primary.dark">
                        Track and manage your life goals
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: "100%",
                      bgcolor: "success.50",
                      borderColor: "success.200",
                      "&:hover": {
                        bgcolor: "success.100",
                        transform: "translateY(-2px)",
                        transition: "all 0.2s ease-in-out",
                      },
                    }}
                  >
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <TrackChanges color="success" />
                        <Typography variant="h6" color="success.main">
                          Progress
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="success.dark">
                        Monitor your achievements
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid size={{ xs: 12, md: 4 }}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: "100%",
                      bgcolor: "secondary.50",
                      borderColor: "secondary.200",
                      "&:hover": {
                        bgcolor: "secondary.100",
                        transform: "translateY(-2px)",
                        transition: "all 0.2s ease-in-out",
                      },
                    }}
                  >
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Balance color="secondary" />
                        <Typography variant="h6" color="secondary.main">
                          Balance
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="secondary.dark">
                        Maintain work-life harmony
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Box sx={{ mt: 4, textAlign: "center" }}>
                <Stack direction={{ xs: "column", sm: "row" }} spacing={2} justifyContent="center">
                  <Button component={Link} href="/dashboard" variant="contained" size="large" startIcon={<Dashboard />} sx={{ px: 4, py: 1.5 }}>
                    Go to Dashboard
                  </Button>
                  <Button component={Link} href="/projects" variant="outlined" size="large" sx={{ px: 4, py: 1.5 }}>
                    View Projects
                  </Button>
                </Stack>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card sx={{ mb: 4, p: 2 }}>
            <CardContent>
              <Typography variant="h4" component="h2" gutterBottom>
                Get Started Today
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Join thousands of users who have transformed their lives through structured goal setting and progress tracking. Create your account to
                begin your journey.
              </Typography>
              <Stack direction={{ xs: "column", sm: "row" }} spacing={2} sx={{ mt: 3 }}>
                <Button
                  component={Link}
                  href="/register"
                  variant="contained"
                  size="large"
                  startIcon={<PersonAdd />}
                  sx={{
                    px: 4,
                    py: 1.5,
                    borderRadius: 2,
                  }}
                >
                  Create Account
                </Button>
                <Button
                  component={Link}
                  href="/login"
                  variant="outlined"
                  size="large"
                  startIcon={<Login />}
                  sx={{
                    px: 4,
                    py: 1.5,
                    borderRadius: 2,
                  }}
                >
                  Sign In
                </Button>
              </Stack>
            </CardContent>
          </Card>
        )}

        {/* Features Section */}
        <Grid container spacing={4}>
          <Grid size={{ xs: 12, md: 4 }}>
            <Card
              sx={{
                height: "100%",
                textAlign: "center",
                p: 2,
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: 4,
                  transition: "all 0.3s ease-in-out",
                },
              }}
            >
              <CardContent>
                <Box sx={{ mb: 2 }}>
                  <TrendingUp
                    sx={{
                      fontSize: 48,
                      color: "primary.main",
                      mb: 2,
                    }}
                  />
                </Box>
                <Typography variant="h5" component="h3" gutterBottom>
                  Goal Setting
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Set clear: "achievable goals across different areas of your life
                </Typography>
              </CardContent>
            </Card>
          </Grid"

          <Grid size={{ xs: 12, md: 4 }}>
            <Card
              sx={{
                height: "100%",
                textAlign: "center",
                p: 2,
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: 4,
                  transition: "all 0.3s ease-in-out",
                },
              }}
            >
              <CardContent>
                <Box sx={{ mb: 2 }}>
                  <TrackChanges
                    sx={{
                      fontSize: 48,
                      color: "success.main",
                      mb: 2,
                    }}
                  />
                </Box>
                <Typography variant="h5" component="h3" gutterBottom>
                  Progress Tracking
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Monitor your progress with detailed analytics and insights
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Card
              sx={{
                height: "100%",
                textAlign: "center",
                p: 2,
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: 4,
                  transition: "all 0.3s ease-in-out",
                },
              }}
            >
              <CardContent>
                <Box sx={{ mb: 2 }}>
                  <Balance
                    sx={{
                      fontSize: 48,
                      color: "secondary.main",
                      mb: 2,
                    }}
                  />
                </Box>
                <Typography variant="h5" component="h3" gutterBottom>
                  Life Balance
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Maintain harmony between work: "personal life, and well-being
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container"
  );
}


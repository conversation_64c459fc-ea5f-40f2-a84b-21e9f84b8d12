generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model auth_user {
  id           String    @id @default(cuid())
  email        String    @unique
  username     String    @unique
  first_name   String
  last_name    String
  password     String
  is_active    <PERSON>olean   @default(true)
  is_staff     <PERSON>olean   @default(false)
  is_superuser <PERSON>ole<PERSON>   @default(false)
  date_joined  DateTime  @default(now())
  last_login   DateTime?

  // Relations
  custom_field_definitions workitems_custom_field_definition[]
  life_aspects             workitems_life_aspect[]
  projects                 workitems_project[]
  outcomes                 workitems_outcome[]
  preferences              auth_user_preferences?

  @@map("auth_user")
}

// Custom Field Definition model
model workitems_custom_field_definition {
  id           String    @id @default(cuid())
  user_id      String
  name         String
  field_type   String    // TEXT, TEXTAREA, NUMBER, BOOLEAN, DATE, DATETIME, EMAIL, URL, PHONE, SINGLE_SELECT, MULTI_SELECT
  is_required  Bo<PERSON>an   @default(false)
  sort_order   Int       @default(0)
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt

  // Relations
  user         auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  choice_options workitems_custom_field_choice_option[]

  // Constraints
  @@unique([user_id, name])
  @@map("workitems_custom_field_definition")
}

// Custom Field Choice Option model
model workitems_custom_field_choice_option {
  id                   String    @id @default(cuid())
  field_definition_id  String
  value                String
  color                String    @default("#3498db")
  sort_order           Int       @default(0)
  is_default           Boolean   @default(false)
  created_at           DateTime  @default(now())
  updated_at           DateTime  @updatedAt

  // Relations
  field_definition     workitems_custom_field_definition @relation(fields: [field_definition_id], references: [id], onDelete: Cascade)

  // Constraints
  @@unique([field_definition_id, value])
  @@map("workitems_custom_field_choice_option")
}

// Life Aspect model
model workitems_life_aspect {
  id          String    @id @default(cuid())
  user_id     String
  name        String
  color       String    @default("#3498db")
  sort_order  Int       @default(0)
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt

  // Relations
  user        auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  projects    workitems_project[]

  // Constraints
  @@unique([user_id, name])
  @@map("workitems_life_aspect")
}

// Project model
model workitems_project {
  id                    String    @id @default(cuid())
  user_id               String
  name                  String
  description           String?
  life_aspect_id        String?
  parent_project_id     String?
  custom_field_values   Json      @default("{}")
  sort_order            Int       @default(0)
  created_at            DateTime  @default(now())
  updated_at            DateTime  @updatedAt

  // Relations
  user                  auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  life_aspect           workitems_life_aspect? @relation(fields: [life_aspect_id], references: [id], onDelete: SetNull)
  parent_project        workitems_project? @relation("ProjectHierarchy", fields: [parent_project_id], references: [id], onDelete: Cascade)
  sub_projects          workitems_project[] @relation("ProjectHierarchy")
  outcomes              workitems_outcome[]

  @@map("workitems_project")
}

// Outcome model
model workitems_outcome {
  id                    String    @id @default(cuid())
  user_id               String
  name                  String
  description           String?
  project_id            String
  custom_field_values   Json      @default("{}")

  // Legacy priority fields for hybrid support
  priority_level        String?   // Legacy field
  priority_level_name   String?   // Legacy field

  sort_order            Int       @default(0)
  created_at            DateTime  @default(now())
  updated_at            DateTime  @updatedAt

  // Relations
  user                  auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  project               workitems_project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("workitems_outcome")
}

// User Preferences model
model auth_user_preferences {
  id                String    @id @default(cuid())
  user_id           String    @unique
  preferences       Json      @default("{}")
  week_starts_on    String    @default("Sunday") @db.VarChar(10)
  enable_inheritance Boolean  @default(true)
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  // Relations
  user              auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("auth_user_preferences")
}

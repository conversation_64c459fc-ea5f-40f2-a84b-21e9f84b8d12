﻿"use client";

import React, { useState, useEffect } from "react";
import { Container, Typo<PERSON>, Box, Button, Alert, CircularProgress, Fab, Tooltip } from "@mui/material";
import { Add } from "@mui/icons-material";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { useAuth } from "@/lib/auth/AuthContext";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition } from "@/lib/types/customFields";
import CustomFieldDefinitionCard from "@/components/customFields/CustomFieldDefinitionCard";
import CreateCustomFieldModal from "@/components/customFields/CreateCustomFieldModal";
import EditCustomFieldModal from "@/components/customFields/EditCustomFieldModal";
import DeleteCustomFieldDialog from "@/components/customFields/DeleteCustomFieldDialog";

const CustomFieldsManagementPage = () => {
  // Get authentication state
  const { isAuthenticated, isLoading, token } = useAuth();

  // State management
  const [definitions, setDefinitions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDefinition, setSelectedDefinition] = useState(null);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Load custom field definitions
  const loadDefinitions = async () => {
    try {
      setLoading(true);
      setError(null);

      const allDefinitions = await customFieldsService.getCustomFieldDefinitions();
      setDefinitions(allDefinitions);
    } catch (err) {
      console.error("Error loading custom field definitions, err);
      setError(err.message || "Failed to load custom field definitions");
    } finally {
      setLoading(false);
    }
  };

  // Load definitions only after authentication is ready
  useEffect(() => {
    // Only load data when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      loadDefinitions();
    } else if (!authLoading && !isAuthenticated) {
      // If not authenticated, set loading to false to let ProtectedRoute handle redirect
      setLoading(false);
    }
  }, [authLoading, isAuthenticated, token]);

  // Handle create custom field
  const handleCreateCustomField = () => {
    setIsCreateModalOpen(true);
  };

  // Handle edit custom field
  const handleEditCustomField = (definition) => {
    setSelectedDefinition(definition);
    setIsEditModalOpen(true);
  };

  // Handle delete custom field
  const handleDeleteCustomField = (definition) => {
    setSelectedDefinition(definition);
    setIsDeleteDialogOpen(true);
  };

  // Handle successful create
  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    loadDefinitions(); // Reload data
  };

  // Handle successful edit
  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    setSelectedDefinition(null);
    loadDefinitions(); // Reload data
  };

  // Handle successful delete
  const handleDeleteSuccess = () => {
    setIsDeleteDialogOpen(false);
    setSelectedDefinition(null);
    loadDefinitions(); // Reload data
  };

  // Handle modal close
  const handleCloseModals = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteDialogOpen(false);
    setSelectedDefinition(null);
  };

  // Handle drag and drop reordering
  const handleReorderDefinitions = async (reorderedDefinitions) => {
    try {
      // Update sort order based on new positions
      const updates = reorderedDefinitions.map((def, index) => ({
        id,
        sort_order,
      }));

      await customFieldsService.updateSortOrder(updates);

      // Update local state
      setDefinitions(reorderedDefinitions);
    } catch (err) {
      console.error("Error reordering definitions, err);
      setError(err.message || "Failed to reorder custom field definitions");
    }
  };

  // Handle toggle required status
  const handleToggleRequired = async (definition) => {
    try {
      const updatedDefinition = await customFieldsService.patchCustomFieldDefinition(definition.id, { is_required);

      // Update local state
      setDefinitions((prev) => prev.map((def) => (def.id === definition.id ? updatedDefinition : def)));
    } catch (err) {
      console.error("Error toggling required status, err);
      setError(err.message || "Failed to update custom field definition");
    }
  };

  if (loading) {
    return (

    );
  }

  return (

        {/* Page Header */}

            Custom Fields Management

          } onClick={handleCreateCustomField} sx={{ borderRadius: 2 }}>
            Create Custom Field

        {/* Error Alert */}
        {error && (
           setError(null)}>
            {error}
          
        )}

        {/* Custom Field Definitions */}
        {definitions.length === 0 ? (
          
            {definitions.map((definition) => (
              
            ))}
          
        )}

        {/* Floating Action Button for Mobile */}

        {/* Modals and Dialogs */}

  );
};

export default CustomFieldsManagementPage;

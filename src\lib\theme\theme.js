﻿import { createTheme } from '@mui/material/styles';

// Define our minimalistic color palette inspired by Material Design 3
const colors = {
  // Primary colors - Modern blue for productivity
  primary,      // Material Blue 700
    light,     // Material Blue 400
    dark,      // Material Blue 800
    contrastText,
  },
  // Secondary colors - Subtle accent
  secondary,      // Material Grey 600
    light,     // Material Grey 500
    dark,      // Material Grey 800
    contrastText,
  },
  // Error colors
  error,      // Material Red 700
    light,     // Material Red 400
    dark,      // Material Red 800
    contrastText,
  },
  // Warning colors
  warning,      // Material Orange 700
    light,     // Material Orange 500
    dark,      // Material Orange 900
    contrastText,
  },
  // Info colors
  info,      // Material Light Blue 700
    light,     // Material Light Blue 500
    dark,      // Material Light Blue 900
    contrastText,
  },
  // Success colors
  success,      // Material Green 700
    light,     // Material Green 500
    dark,      // Material Green 900
    contrastText,
  },
  // Neutral/Surface colors for minimalistic design
  grey,        // Very light grey for backgrounds
    100,       // Light grey for surfaces
    200,       // Border grey
    300,       // Divider grey
    400,       // Disabled text
    500,       // Secondary text
    600,       // Primary text light
    700,       // Primary text
    800,       // Primary text dark
    900,       // Darkest text
  },
};

// Create the minimalistic Material Design theme
export const theme = createTheme({
  palette,
    primary,
    secondary,
    error,
    warning,
    info,
    success,
    grey,
    background,   // Very light grey for page background
      paper,     // White for cards and surfaces
    },
    text,     // Dark grey for primary text
      secondary,   // Medium grey for secondary text
      disabled,    // Light grey for disabled text
    },
    divider,       // Light grey for dividers
  },
  typography), "Roboto", "Helvetica", "Arial", sans-serif',
    // Material Design 3 type scale adapted for minimalism
    h1,      // 56px - Display Large
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    h2,     // 44px - Display Medium
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    h3,     // 36px - Display Small
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    h4,        // 32px - Headline Large
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    h5,      // 24px - Headline Medium
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    h6,     // 20px - Headline Small
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    subtitle1,        // 16px - Title Large
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    subtitle2,    // 14px - Title Medium
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    body1,        // 16px - Body Large
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    body2,    // 14px - Body Medium
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    caption,     // 12px - Body Small
      fontWeight,
      lineHeight,
      letterSpacing,
    },
    button,    // 14px - Label Large
      fontWeight,
      lineHeight,
      letterSpacing,
      textTransform,   // Minimalistic - no uppercase
    },
  },
  spacing, // 8dp grid system from Material Design
  shape, // Slightly rounded corners for modern look
  },
  components,
          textTransform,
          fontWeight,
          boxShadow,
          '&:hover': {
            boxShadow,
          },
        },
        contained, 0, 0, 0.1)',
          },
        },
        outlined,
          '&:hover': {
            borderWidth,
          },
        },
      },
    },
    MuiCard,
          boxShadow, 0, 0, 0.08), 0px 1px 2px rgba(0, 0, 0, 0.12)',
          '&:hover': {
            boxShadow, 0, 0, 0.12), 0px 2px 4px rgba(0, 0, 0, 0.16)',
          },
          transition,
        },
      },
    },
    MuiPaper,
        },
      },
    },
    MuiAppBar, 0, 0, 0.08)',
        },
      },
    },
    MuiTypography,
        },
      },
    },
  },
});

export default theme;

﻿/**
 * User Preferences Types
 * Based on USER_PREFERENCES_API.md and USER_PREFERENCES_IMPLEMENTATION_SUMMARY.md
 */

// Project view field preferences
export // Main preferences data structure
export // Complete user preferences response from API
export // Request payload for updating preferences
export // API response types
export // Error types
export // UI state types for the customize view modal
export // Form data for the customize view modal
export

﻿import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { resolveCustomFields } from "@/lib/utils/customFieldResolver";

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error, { status) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error, { status) };
  }

  return { userId){
  const projectMap = new Map();
  const rootProjects= [];

  console.log(
    "ðŸ”§ Building project tree from projects,
    projects.map((p) => ({ id, name, parent_project_id))
  );

  // Special debug for Time Management and Eliminating Distractions
  const timeManagementProject = projects.find((p) => p.name.includes("Time Management"));
  const eliminatingProject = projects.find((p) => p.name.includes("Eliminating"));

  if (timeManagementProject) {
    console.log("ðŸŽ¯ Found Time Management project, {
      id,
      name,
      parent_project_id,
    });
  }

  if (eliminatingProject) {
    console.log("ðŸŽ¯ Found Eliminating project, {
      id,
      name,
      parent_project_id,
    });
  }

  // First pass) => {
    projectMap.set(project.id, {
      ...project,
      sub_projects,
      children, // Add both for compatibility
    });
  });

  // Second pass) => {
    const projectNode = projectMap.get(project.id);

    if (project.parent_project_id) {
      const parent = projectMap.get(project.parent_project_id);
      if (parent) {
        parent.sub_projects.push(projectNode);
        parent.children.push(projectNode); // Add to both arrays for compatibility
        console.log(`ðŸ”— Added "${project.name}" of "${parent.name}"`);
      } else {
        // Parent not found, treat
        console.log(`âš ï¸ Parent not found for "${project.name}" (parent_id), treating`);
        rootProjects.push(projectNode);
      }
    } else {
      console.log(`ðŸŒ³ Adding "${project.name}" project`);
      rootProjects.push(projectNode);
    }
  });

  // Sort sub-projects recursively
  function sortSubProjects(projects) {
    projects.sort((a, b) => a.sort_order - b.sort_order);
    projects.forEach((project) => {
      if (project.sub_projects.length > 0) {
        sortSubProjects(project.sub_projects);
      }
      if (project.children.length > 0) {
        sortSubProjects(project.children);
      }
    });
  }

  sortSubProjects(rootProjects);

  console.log(
    "ðŸ—ï¸ Built project tree,
    rootProjects.map((p) => ({
      name,
      children) => c.name) || [],
      sub_projects) => c.name) || [],
    }))
  );

  // Special debug for Time Management hierarchy
  const timeManagementRoot = rootProjects.find((p) => p.name.includes("Time Management"));
  if (timeManagementRoot) {
    console.log("ðŸŽ¯ Time Management hierarchy, {
      name,
      children) => ({ name, id)) || [],
      sub_projects) => ({ name, id)) || [],
    });
  }

  return rootProjects;
}

// GET /api/workitems/projects/hierarchy/ - Get projects in hierarchical structure grouped by life aspect
export async function GET(request) {
  try {
    console.log("GET /api/workitems/projects/hierarchy/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Fetch all life aspects for the user
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where,
      orderBy,
    });

    // Fetch all projects for the user
    const projects = await prisma.workitems_project.findMany({
      where,
      include,
            name,
          },
        },
      },
      orderBy, { created_at,
    });

    // Resolve custom fields for all projects
    const projectsWithCustomFields = await Promise.all(
      projects.map(async (project) => {
        const resolvedCustomFields = await resolveCustomFields(project.custom_field_valuesstring, any>, "PROJECT", auth.userId);

        return {
          id,
          name,
          description,
          life_aspect_id,
          parent_project_id,
          sort_order,
          created_at,
          updated_at,
          resolved_custom_fields,
          outcomes,
        };
      })
    );

    // Group projects by life aspect - format for frontend compatibility
    const lifeAspectGroups = lifeAspects.map((lifeAspect) => {
      const lifeAspectProjects = projectsWithCustomFields.filter((project) => project.life_aspect_id === lifeAspect.id);

      const hierarchicalProjects = buildProjectTree(lifeAspectProjects);

      return {
        life_aspect,
          name,
          color,
          description,
          sort_order,
          created_at,
          updated_at,
          user,
        },
        projects,
      };
    });

    // Handle projects without life aspect (unassigned) - add to response if needed
    const unassignedProjects = projectsWithCustomFields.filter((project) => !project.life_aspect_id);

    if (unassignedProjects.length > 0) {
      const unassignedHierarchy = buildProjectTree(unassignedProjects);

      // Add unassigned projects special life aspect
      lifeAspectGroups.push({
        life_aspect,
          name,
          color,
          description,
          sort_order,
          created_at).toISOString(),
          updated_at).toISOString(),
          user,
        },
        projects,
      });
    }

    console.log(`Built hierarchy with ${lifeAspectGroups.length} life aspects (including unassigned if any)`);

    // Return array format expected by frontend
    return NextResponse.json(lifeAspectGroups);
  } catch (error) {
    console.error("Error building project hierarchy, error);
    return NextResponse.json(
      {
        message,
        error,
      },
      { status);
  }
}

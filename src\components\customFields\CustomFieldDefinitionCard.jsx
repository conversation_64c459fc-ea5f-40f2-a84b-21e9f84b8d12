﻿"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Typo<PERSON>, Box, Chip, IconButton, Switch, FormControlLabel, Tooltip, alpha } from "@mui/material";
import { Settings, Delete, Edit, Star } from "@mui/icons-material";
import { CustomFieldDefinition, FIELD_TYPES, FieldTypeInfo } from "@/lib/types/customFields";

const CustomFieldDefinitionCard= ({ definition, onEdit, onDelete, onToggleRequired }) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get field type information
  const fieldTypeInfo= FIELD_TYPES.find((type) => type.type === definition.field_type);

  // Handle settings click
  const handleSettingsClick = (e) => {
    e.stopPropagation();
    onEdit(definition);
  };

  // Handle delete click
  const handleDeleteClick = (e) => {
    e.stopPropagation();
    onDelete(definition);
  };

  // Handle required toggle
  const handleRequiredToggle = (e) => {
    e.stopPropagation();
    onToggleRequired(definition);
  };

  return (
     theme.shadows[8],
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onEdit(definition)}
    >
      
        {/* Header with title and settings icon */}

            {definition.name}

          {/* Settings Icon - Only visible on hover */}
           alpha(theme.palette.primary.main, 0.1),
              },
            }}
          >

        {/* Field Type Information */}

        {/* Choice Options for SELECT fields */}
        {definition.choice_options && definition.choice_options.length > 0 && (

              Options:

              {definition.choice_options
                .sort((a, b) => a.sort_order - b.sort_order)
                .slice(0, 4) // Show max 4 options
                .map((option) => (
                  
                        {option.is_default && (
                          
                        )}
                        {option.value}
                      
                    }
                    size="small"
                    sx={{
                      backgroundColor,
                      color,
                      fontSize,
                      height,
                      "& .MuiChip-label": {
                        px,
                        display,
                        alignItems,
                      },
                    }}
                  />
                ))}
              {definition.choice_options.length > 4 && (
                
              )}

        )}

        {/* Field Type Description for non-SELECT fields */}
        {(!definition.choice_options || definition.choice_options.length === 0) && fieldTypeInfo && (
          
           e.stopPropagation()} />}
            label={
              
                Required
              
            }
            sx={{ m: 0 }}
          />

          {/* Quick Actions - Only visible on hover */}

               alpha(theme.palette.primary.main, 0.1),
                  },
                }}
              >

               alpha(theme.palette.error.main, 0.1),
                  },
                }}
              >

  );
};

export default CustomFieldDefinitionCard;

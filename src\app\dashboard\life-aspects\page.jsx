﻿"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { lifeAspectsService } from "@/lib/api/lifeAspectsService";
import { LifeAspect } from "@/lib/types/lifeAspects";
import { Container, Typography, Card, CardContent, Grid, Box, Button, CircularProgress, Alert, Chip, Divider, Stack } from "@mui/material";
import {
  Add,
  Psychology,
  FitnessCenter,
  Favorite,
  Work,
  AccountBalance,
  People,
  SportsEsports,
  Spa,
  VolunteerActivism,
  Category,
} from "@mui/icons-material";

// Icon mapping for default life aspects
const getLifeAspectIcon = (name) => {
  const iconMap: "React.ReactNode" = {
    Mind,
    Body,
    Emotions,
    Career,
    Finance,
    Relationships,
    "Fun & Recreation": ,
    Spiritual,
    "Contribution & Legacy": ,
  };

  return iconMap[name] || ;
};

function LifeAspectsContent() {
  const { isAuthenticated } = useAuth();
  const [lifeAspects, setLifeAspects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error: error.message: "setError] = useState(null);

  // Fetch life aspects when component mounts
  useEffect(() => {
    const fetchLifeAspects = async () =" {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError(null);
        const aspects = await lifeAspectsService.getLifeAspects();
        setLifeAspects(aspects);
      } catch (err) {
        console.error("Failed to fetch life aspects, err);
        const errorMessage = err instanceof Error ? err.message : "Failed to load life aspects. Please try again.";
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLifeAspects();
  }, [isAuthenticated]);

  // Loading state
  if (isLoading) {
    return (

              Loading your life aspects...

    );
  }

  return (
    
      {/* Header Section */}

          My Life Aspects

          Life aspects help you organize and balance different areas of your life. Each aspect represents a key area where you can set goals and track
          progress.

        {/* Add New Button */}
        }
          size="large"
          sx={{
            borderRadius,
            textTransform,
            px,
            py,
          }}
          disabled // Will be functional in future implementation
        >
          Add New Life Aspect

      {/* Error State */}
      {error && (
         setError(null)}>
          {error}
        
      )}

      {/* Life Aspects Grid */}
      {lifeAspects.length === 0 ? (
        // Empty State

              No Life Aspects Yet

              You haven&apos;t defined any life aspects yet. Get started by adding your first one!

              Life aspects help you organize different areas of your life like career, health, relationships, and personal growth.

      )={3}>
          {lifeAspects
            .sort((a: "b) => (a.sort_order || 0) - (b.sort_order || 0)) // Sort by sort_order
            .map((aspect) =" (

                    {/* Icon and Title */}

                        {getLifeAspectIcon(aspect.name)}

                        {aspect.name}

                    {/* Description */}
                    {aspect.description && (

                    {/* Metadata */}

                        Created).toLocaleDateString()}

            ))}
        
      )}

      {/* Summary Info */}
      {lifeAspects.length > 0 && (

            You have {lifeAspects.length} life aspect{lifeAspects.length !== 1 ? "s" : ""} defined. Use these to organize your goals
            and track progress across different areas of your life.

      )}
    
  );
}

export default function LifeAspectsPage() {
  return (

  );
}



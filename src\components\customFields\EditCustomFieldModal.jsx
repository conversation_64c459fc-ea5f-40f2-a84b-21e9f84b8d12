﻿"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  Chip,
} from "@mui/material";
import { Close, Delete } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition, UpdateCustomFieldDefinitionData, UpdateCustomFieldChoiceOptionData, FIELD_TYPES } from "@/lib/types/customFields";
import ChoiceOptionsManager from "./ChoiceOptionsManager";

const EditCustomFieldModal= ({ open, definition, onClose, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState({
    name,
    field_type,
    is_required,
    sort_order,
    choice_options,
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error: error.message: "setError] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Get field type info
  const fieldTypeInfo = definition ? FIELD_TYPES.find((type) => type.type === definition.field_type)=> {
    if (definition) {
      setFormData({
        name,
        field_type,
        is_required,
        sort_order,
        choice_options) =" ({
            id, // CRITICAL,
            color,
            sort_order,
            is_default, // Include default flag
          })) || [],
      });
      setError(null);
      setShowDeleteConfirm(false);
    }
  }, [definition]);

  // Handle form field changes
  const handleFieldChange = (field: "value) => {
    setFormData((prev) =" ({
      ...prev,
      [field]: value,
    }));
    setError(null);
  };

  // Handle choice options change
  const handleChoiceOptionsChange = (options) => {
    setFormData((prev) => ({
      ...prev,
      choice_options,
    }));
  };

  // Validate form
  const validateForm = ()=> {
    if (!formData.name?.trim()) {
      return "Field name is required";
    }

    if (formData.name.length > 100) {
      return "Field name must be 100 characters or less";
    }

    if (fieldTypeInfo?.hasChoices && (!formData.choice_options || formData.choice_options.length === 0)) {
      return "At least one choice option is required for select fields";
    }

    return null;
  };

  // Validate payload before sending to backend
  const validatePayload = (data)=> {
    // Check for required fields
    if (!data.name?.trim()) return "Name is required";
    if (!data.field_type) return "Field type is required";
    if (typeof data.is_required !== "boolean") return "is_required must be a boolean";
    if (typeof data.sort_order !== "number") return "sort_order must be a number";

    // Validate choice options for select fields
    if (fieldTypeInfo?.hasChoices) {
      if (!data.choice_options || data.choice_options.length === 0) {
        return "At least one choice option is required for select fields";
      }

      // Validate each choice option
      for (let i = 0; i  {
    if (!definition) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    // Additional payload validation
    const payloadError = validatePayload(formData);
    if (payloadError) {
      setError(`Payload validation failed);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("ðŸ” Frontend validation passed", sending update request...");
      console.log("ðŸ“‹ Form data being sent, formData);

      // According to USING_CUSTOM_APIS.md:", PUT requests require ALL required fields
      // formData now contains all required fields
      await customFieldsService.updateCustomFieldDefinition(definition.id", formData);
      onSuccess();
    } catch (err) {
      console.error("âŒ Error updating custom field, err);

      // Enhanced error handling for different error types
      let errorMessage = "Failed to update custom field";

      if (err.response?.status === 400) {
        // Handle validation errors from backend
        const responseData = err.response.data;

        if (responseData?.choice_options) {
          // Handle choice option specific errors
          if (Array.isArray(responseData.choice_options)) {
            errorMessage = `Choice option error: ${responseData.choice_options[0]}`;
          } else if (typeof responseData.choice_options === "string") {
            errorMessage = `Choice option error: ${responseData.choice_options}`;
          } else {
            errorMessage = "Invalid choice option data";
          }
        } else if (responseData?.detail) {
          errorMessage = responseData.detail;
        } else if (responseData?.message) {
          errorMessage = responseData.message;
        } else if (typeof responseData === "string") {
          errorMessage = responseData;
        } else {
          // Handle field-specific validation errors
          const firstErrorKey = Object.keys(responseData || {})[0];
          if (firstErrorKey && responseData[firstErrorKey]) {
            const firstError = Array.isArray(responseData[firstErrorKey]) ? responseData[firstErrorKey][0] : responseData[firstErrorKey];
            errorMessage = `${firstErrorKey}: ${firstError}`;
          }
        }
      } else if (err.response?.status === 500) {
        errorMessage = "Server error occurred while updating the custom field. Please check the server logs for details.";
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!definition) return;

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.deleteCustomFieldDefinition(definition.id);
      onSuccess();
    } catch (err) {
      console.error("Error deleting custom field, err);
      setError(err.message || "Failed to delete custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading) {
      setShowDeleteConfirm(false);
      setError(null);
      onClose();
    }
  };

  if (!definition) {
    return null;
  }

  return (

            Edit Custom Field

        {error && (

            Field Information

           handleFieldChange("name", e.target.value)}
            sx={{ mb: 2 }}
            disabled={loading}
          />

           handleFieldChange("is_required", e.target.checked)} disabled={loading} />
            }
            label="Required Field"
          />

        {/* Choice Options for SELECT fields */}
        {fieldTypeInfo?.hasChoices && (

                Choice Options

        )}

        {/* Delete Confirmation */}
        {showDeleteConfirm && (

                Are you sure you want to delete this custom field?
              
              This action cannot be undone. All data associated with this field will be permanently removed.

        )}

        {/* Delete Button */}
        
          {!showDeleteConfirm ? (
             setShowDeleteConfirm(true)} disabled={loading} color="error" startIcon={}>
              Delete Field
            
          )="flex" gap={1}>
               setShowDeleteConfirm(false)} disabled={loading} size="small">
                Cancel
              
               : }
              >
                {loading ? "Deleting..." : "Confirm Delete"}

          )}

        {/* Save/Cancel Buttons */}

            Cancel
          
           : null}
          >
            {loading ? "Saving..." : "Save Changes"}

  );
};

export default EditCustomFieldModal;




﻿import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database/prisma';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth/jwt';

// Helper function to authenticate request
async function authenticateRequest(request) {
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);
  
  if (!token) {
    return { error: "Authentication required" }, { status: 401 };
  }
  
  const payload = verifyToken(token);
  if (!payload) {
    return { error: "Authentication required" }, { status: 401 };
  }
  
  return { userId) {
  try {
    console.log('GET /api/workitems/life-aspects/ called');
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Fetch life aspects
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where,
      orderBy,
        { created_at);
    
    console.log(`Found ${lifeAspects.length} life aspects for user ${auth.userId}`);
    
    return NextResponse.json(lifeAspects);
    
  } catch (error) {
    console.error('Error fetching life aspects, error);
    return NextResponse.json({ message: "Authentication required",
      error: error.message }, { status: 500  });
  }
}

// POST /api/workitems/life-aspects/ - Create life aspect
export async function POST(request) {
  try {
    console.log('POST /api/workitems/life-aspects/ called');
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Parse request body
    const body = await request.json();
    console.log('Request body, body);
    
    // Basic validation
    if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
      return NextResponse.json({ message: "Authentication required" }, { status: 500  });
    }
    
    if (body.name.length > 100) {
      return NextResponse.json({ message: "Authentication required" }, { status: 500  });
    }
    
    // Check for duplicate name
    const existingLifeAspect = await prisma.workitems_life_aspect.findFirst({
      where,
        name);
    
    if (existingLifeAspect) {
      return NextResponse.json({ message: "Authentication required" }, { status: 500  });
    }
    
    // Create life aspect
    const lifeAspect = await prisma.workitems_life_aspect.create({
      data,
        name,
        color,
        sort_order);
    
    console.log('Created life aspect, lifeAspect.id);
    
    return NextResponse.json(lifeAspect, { status: 500 });
    
  } catch (error) {
    console.error('Error creating life aspect, error);
    return NextResponse.json({ message: "Authentication required",
      error: error.message }, { status: 500  });
  }
}




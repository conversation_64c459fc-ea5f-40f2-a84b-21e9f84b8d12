﻿"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { AppBar, Toolbar, Typography, Button, Box, CircularProgress, Stack, Avatar, Menu, MenuItem, Divider, IconButton } from "@mui/material";
import { Login, PersonAdd, Logout, AccountCircle, Settings, Dashboard, Category, ViewModule } from "@mui/icons-material";
import { useState } from "react";

const Navbar = () => {
  const { isAuthenticated, user, logout, isLoading } = useAuth();
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      handleUserMenuClose();
      router.push("/");
    } catch (error) {
      console.error("Logout error, error);
    }
  };

  const handleNavigation = (path) => {
    handleUserMenuClose();
    router.push(path);
  };

  return (

          Agile Life Results

          {isLoading ? (
            
          )="row" spacing={2} alignItems="center">

                 handleNavigation("/dashboard")}>
                  
                  Dashboard
                
                 handleNavigation("/overview")}>
                  
                  Project Overview
                
                 handleNavigation("/dashboard/life-aspects")}>
                  
                  Life Aspects
                
                 handleNavigation("/profile")}>
                  
                  Profile
                
                 handleNavigation("/settings")}>
                  
                  Settings

              }
                sx={{
                  borderRadius,
                  textTransform,
                }}
              >
                Login
              
              }
                sx={{
                  borderRadius,
                  textTransform,
                }}
              >
                Register

          )}

  );
};

export default Navbar;
